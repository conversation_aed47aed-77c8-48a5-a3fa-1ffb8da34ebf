export interface IBidder {
  winbidQuantityList: number[];
  sellNameList: string[];
  applyQuantityList: number[];
}
export interface ITradeStatus {
  countList: number[];
  statusList: string[];
}
export interface ITradeList {
  total: 3;
  current: 1;
  pageSize: 10;
  list: ITradeData[];
}
export interface ITradeData {
  sellId: number;
  time: string;
  quantity: number;
  price: number;
  startTime: string;
  endTime: string;
  status: string;
  name: string;
  type: null;
}
export interface ITradeStrategy {
  applyBuyRecord: IApplyStrategy;
  winbidRecord: IWinbidResult;
}
export interface IApplyStrategy {
  applyId: number;
  sellId: number;
  time: string;
  applyQuantity: number;
  applyPrice: number;
  startTime: string;
  endTime: string;
  name: null;
  [key: string]: any;
}
export interface IWinbidResult {
  winbidId: number;
  applyId: number;
  time: string;
  winbidQuantity: number;
  price: number;
  startTime: string;
  endTime: string;
  name: null;
  [key: string]: any;
}
export interface INameList {
  winbidId: number;
  name: string;
}
export interface IApplyData {
  curveList: number[];
  originalCurveList: number[];
  status: '未申报' | '已申报';
}
export interface ITradeRank {
  list: ITradeRankData[];
}
export interface ITradeRankData {
  actualQuantity: number;
  expectedQuantity: number;
  name: string;
}
export interface IResourceList {
  current: number;
  pageSize: number;
}
export interface IResultList {
  resourceId: number;
  name: string;
  type: string;
  deleted: null;
}
export interface IStatistic {
  tradeFourData: {
    thisYearSellCount: number;
    thisYearWinbidCount: number;
    thisYearSellQuantity: number;
    thisYearWinbidQuantity: number;
  };
}
