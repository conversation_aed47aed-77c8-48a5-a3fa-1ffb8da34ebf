@import '~@/assets/css/helpers.sass'

.box
    width: 100%
    height: px(910)
.time
    font-size: px(18)
    color: white
    height: px(30)
    display: flex
    flex-direction: row
    align-items: center
.content
    width: 100%
    height: calc( 100% - px(50) )
    display: flex
    flex-wrap: wrap
    justify-content: space-between
.pane
    width: calc( 50% - px(10) )
    height: calc( 50% - px(0))
.pane2
    width: 100%
    height: calc( 50% - px(20))

.datePicker
    :global
        .ant-picker
            width: px(200) !important
      
           
            background: rgba(0,0,0,0) !important
            border: white solid px(10) !important    
            line-height: 0
            height: px(34) !important
        .ant-picker-input > input
            color: white
            background: rgba(0,0,0,0)
        .ant-picker-suffix
            color: white
        .ant-picker-separator
            color: white
