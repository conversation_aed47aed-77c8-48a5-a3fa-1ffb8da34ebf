import px from '@/utils/px';
import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import styles from './index.sass';
interface AdjustmentListStepProps {
  data: any[];
}

const AdjustmentListStep: React.FC<AdjustmentListStepProps> = ({ data }) => {
  // 第一步：调节量清单表格列配置
  const columns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'dateStr',
      key: 'dateStr',
      width: px(100),
      align: 'center',
    },
    {
      title: '整体基线(MW)',
      dataIndex: 'wholeBaseline',
      key: 'wholeBaseline',
      width: px(100),
      align: 'center',
    },
    {
      title: '整体调节量(MW)',
      dataIndex: 'wholeAdjust',
      key: 'wholeAdjust',
      width: px(100),
      align: 'center',
    },
    {
      title: '特来电基线(MW)',
      dataIndex: 'teldBaseline',
      key: 'teldBaseline',
      width: px(100),
      align: 'center',
    },
    {
      title: '特来电调节量(MW)',
      dataIndex: 'teldAdjust',
      key: 'teldAdjust',
      width: px(100),
      align: 'center',
    },
    {
      title: '电表功率(MW)',
      dataIndex: 'meterPower',
      key: 'meterPower',
      width: px(100),
      align: 'center',
    },
    {
      title: '电表基线(MW)',
      dataIndex: 'meterBaseline',
      key: 'meterBaseline',
      width: px(100),
      align: 'center',
    },
    {
      title: '电表调节量(MW)',
      dataIndex: 'meterAdjust',
      key: 'meterAdjust',
      width: px(100),
      align: 'center',
    },
  ];

  return (
    <div className={styles.stepContent}>
      <h3 className={styles.stepTitle}>每15分钟调节量清单</h3>
      <Table
        dataSource={data}
        columns={columns}
        className={styles.table}
        pagination={false}
        scroll={{ y: px(400) }}
      />
    </div>
  );
};

export default AdjustmentListStep;
