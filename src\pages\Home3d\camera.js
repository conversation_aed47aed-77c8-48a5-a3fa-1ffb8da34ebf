import { useFrame, useThree } from '@react-three/fiber';
import { useEffect } from 'react';
const SetCamera = () => {
  const { camera } = useThree();

  useEffect(() => {
    const scale = 0.00049;

    camera.position.set(12.49 * scale, 1590.45 * scale, 1459.43 * scale);

    camera.lookAt(-0.27 * scale, 60.68 * scale, 64.96 * scale);

    // 调整透视视角
    camera.fov = 50; // 设置为60度

    // 更新相机属性
    camera.updateProjectionMatrix();
  }, [camera]);

  return null;
};

const CameraPositionLogger = () => {
  const { camera } = useThree();

  useFrame(() => {
    console.log(camera.position); // 实时打印摄像机位置
  });

  return null; // 这个组件不渲染任何东西
};

export { CameraPositionLogger, SetCamera };
