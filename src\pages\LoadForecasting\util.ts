import dayjs, { Dayjs } from 'dayjs';

export const defaultTime: Dayjs = dayjs().startOf('day');
export const defaultTimeString = defaultTime.format('YYYY-MM-DD');
export const disabledDate = (current: Dayjs) => {
  const now = dayjs();
  const hour = now.hour();
  const tomorrow = now.add(1, 'day').startOf('day');

  // 如果当前时间在10点前，禁用明天
  if (hour < 10) {
    return (
      current &&
      (current.isAfter(now) ||
        current.isBefore(dayjs('2025-05-29').startOf('day')))
    );
  }

  // 10点后允许选择明天
  return (
    current &&
    (current.isAfter(tomorrow) ||
      current.isBefore(dayjs('2025-05-29').startOf('day')))
  );
};
export const defaultTimeEva: [Dayjs | null, Dayjs | null] = [
  dayjs().add(-6, 'day').startOf('day'),
  dayjs().startOf('day'),
];
export const defaultTimeStringEva = [
  defaultTimeEva[0].format('YYYY-MM-DD'),
  defaultTimeEva[1].format('YYYY-MM-DD'),
];
export const disabledDateEva = (current: Dayjs) => {
  return (
    current &&
    (current.isAfter(dayjs()) ||
      current.isBefore(dayjs('2024-08-07').startOf('day')))
  );
};
