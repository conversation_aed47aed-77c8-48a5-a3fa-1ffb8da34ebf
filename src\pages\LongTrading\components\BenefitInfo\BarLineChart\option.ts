import { UNIT_COLORS } from '@/constants/colors';
import px from '@/utils/px';
// import { chartData } from './mock';

export interface IRevenueData {
  timePoints: string[];

  // 内部交易数据
  innerListingPower?: number[]; // 挂牌电量
  innerMatchingPower?: number[]; // 撮合电量
  innerAdjustmentPower?: number[]; // 月度双边调整电量
  innerListingRevenue?: number[]; // 挂牌收益
  innerMatchingRevenue?: number[]; // 撮合收益
  innerAdjustmentRevenue?: number[]; // 调整收益
  innerTotalRevenue?: number[]; // 内部总收益
  innerPowerUnit?: string; // 内部电量单位
  innerRevenueUnit?: string; // 内部收益单位

  // 外部交易数据
  extraListingPower?: number[]; // 挂牌电量
  extraBiddingPower?: number[]; // 竞价电量
  extraMatchingPower?: number[]; // 撮合电量
  extraListingRevenue?: number[]; // 挂牌收益
  extraBiddingRevenue?: number[]; // 竞价收益
  extraMatchingRevenue?: number[]; // 撮合收益
  extraTotalRevenue?: number[]; // 外部总收益
  extraPowerUnit?: string; // 外部电量单位
  extraRevenueUnit?: string; // 外部收益单位

  // 单位
  totalRevenueUnit?: string; // 总收益单位

  // 类型标识
  type?: 'inner' | 'extra'; // 内部或外部交易
}

export const getOption = (data: IRevenueData) => {
  // 根据类型确定使用内部还是外部数据
  const isInner = data.type === 'inner';

  // 确定电量和收益单位
  const powerUnit = isInner
    ? data.innerPowerUnit || 'MWh'
    : data.extraPowerUnit || 'MWh';
  const revenueUnit = isInner
    ? data.innerRevenueUnit || '元'
    : data.extraRevenueUnit || '元';

  // 计算电量和收益的数据范围
  let powerMin = 0;
  let powerMax = 0;
  let revenueMin = 0;
  let revenueMax = 0;

  // 获取电量数据的最大值和最小值
  const powerData = [];
  if (isInner) {
    if (data.innerListingPower) powerData.push(...data.innerListingPower);
    if (data.innerMatchingPower) powerData.push(...data.innerMatchingPower);
    if (data.innerAdjustmentPower) powerData.push(...data.innerAdjustmentPower);
  } else {
    if (data.extraListingPower) powerData.push(...data.extraListingPower);
    if (data.extraBiddingPower) powerData.push(...data.extraBiddingPower);
    if (data.extraMatchingPower) powerData.push(...data.extraMatchingPower);
  }

  // 获取收益数据的最大值和最小值
  const revenueData = [];
  if (isInner) {
    if (data.innerListingRevenue) revenueData.push(...data.innerListingRevenue);
    if (data.innerMatchingRevenue)
      revenueData.push(...data.innerMatchingRevenue);
    if (data.innerAdjustmentRevenue)
      revenueData.push(...data.innerAdjustmentRevenue);
    if (data.innerTotalRevenue) revenueData.push(...data.innerTotalRevenue);
  } else {
    if (data.extraListingRevenue) revenueData.push(...data.extraListingRevenue);
    if (data.extraBiddingRevenue) revenueData.push(...data.extraBiddingRevenue);
    if (data.extraMatchingRevenue)
      revenueData.push(...data.extraMatchingRevenue);
    if (data.extraTotalRevenue) revenueData.push(...data.extraTotalRevenue);
  }

  if (powerData.length > 0) {
    powerMin = Math.min(
      ...powerData.filter((v) => v !== null && v !== undefined),
    );
    powerMax = Math.max(
      ...powerData.filter((v) => v !== null && v !== undefined),
    );
  }

  if (revenueData.length > 0) {
    revenueMin = Math.min(
      ...revenueData.filter((v) => v !== null && v !== undefined),
    );
    revenueMax = Math.max(
      ...revenueData.filter((v) => v !== null && v !== undefined),
    );
  }

  // 确保最小值不会大于0
  powerMin = Math.min(powerMin, 0);
  revenueMin = Math.min(revenueMin, 0);

  // 计算两组数据范围的比值(相当于比例尺)
  const powerRange = powerMax - powerMin;
  const revenueRange = revenueMax - revenueMin;
  const ratio = powerRange / revenueRange;

  // 两组数据对阶，确定应当使用哪一组的数据作为最大值或最小值
  let y1Max, y1Min, y2Max, y2Min;

  if (powerMax < revenueMax * ratio) {
    y1Max = revenueMax * ratio;
    y2Max = revenueMax;
  } else {
    y1Max = powerMax;
    y2Max = powerMax / ratio;
  }

  if (powerMin < revenueMin * ratio) {
    y1Min = powerMin;
    y2Min = powerMin / ratio;
  } else {
    y1Min = revenueMin * ratio;
    y2Min = revenueMin;
  }

  // 确保最小值不会大于0
  y1Min = Math.min(y1Min, 0);
  y2Min = Math.min(y2Min, 0);

  // 给最大最小值增加一定的空间，避免数据点在边缘
  const padding = 1.1; // 10%的空间
  y1Max = y1Max * padding;
  y2Max = y2Max * padding;
  y1Min = y1Min * padding;
  y2Min = y2Min * padding;

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: function (params: any) {
        let tooltip = params[0].name + '<br/>';

        // 分组显示电量和收益
        let powerItems = [];
        let revenueItems = [];

        params.forEach((item: any) => {
          const marker = item.marker;
          const seriesName = item.seriesName;
          const value = item.value;
          const valueWithUnit =
            item.seriesIndex <= 2
              ? value + ' ' + powerUnit
              : value + ' ' + revenueUnit;

          if (item.seriesIndex <= 2) {
            powerItems.push(marker + ' ' + seriesName + ': ' + valueWithUnit);
          } else {
            revenueItems.push(marker + ' ' + seriesName + ': ' + valueWithUnit);
          }
        });

        if (powerItems.length > 0) {
          tooltip += '<br/><b>电量：</b><br/>' + powerItems.join('<br/>');
        }

        if (revenueItems.length > 0) {
          tooltip += '<br/><b>收益：</b><br/>' + revenueItems.join('<br/>');
        }

        return tooltip;
      },
    },
    legend: {
      type: 'scroll',
      textStyle: {
        color: 'white',
        fontSize: px(14),
      },
    },

    grid: {
      left: '5%',
      right: '5%',
      bottom: '2%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: data.timePoints || [],
      axisLabel: {
        color: 'white',
        fontSize: px(14),
      },
    },
    yAxis: [
      {
        type: 'value',
        name: `电量 (${powerUnit})`,
        position: 'left',
        min: y1Min,
        max: y1Max,
        axisLabel: {
          color: 'white',
          fontSize: px(14),
          formatter: function (value) {
            if (Number.isInteger(value)) {
              return value.toString();
            } else {
              return value.toFixed(2);
            }
          },
        },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLine: {
          lineStyle: {
            color: '#5470C6',
          },
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.1)',
          },
        },
      },
      {
        type: 'value',
        name: `收益 (${revenueUnit})`,
        position: 'right',
        min: y2Min,
        max: y2Max,
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLabel: {
          color: 'white',
          fontSize: px(14),
          formatter: function (value) {
            if (Number.isInteger(value)) {
              return value.toString();
            } else {
              return value.toFixed(2);
            }
          },
        },
        axisLine: {
          lineStyle: {
            color: '#EE6666',
          },
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.1)',
          },
        },
      },
    ],

    series: [
      // 内部交易 - 电量数据（柱状图）
      ...(isInner && data.innerListingPower
        ? [
            {
              name: '挂牌电量',
              type: 'bar',
              yAxisIndex: 0,
              stack: 'power',
              data: data.innerListingPower,
              barMaxWidth: 30,
              itemStyle: {
                color: UNIT_COLORS.listingPower,
              },
            },
          ]
        : []),
      ...(isInner && data.innerMatchingPower
        ? [
            {
              name: '撮合电量',
              type: 'bar',
              yAxisIndex: 0,
              stack: 'power',
              data: data.innerMatchingPower,
              barMaxWidth: 30,
              itemStyle: {
                color: UNIT_COLORS.matchingPower,
              },
            },
          ]
        : []),
      ...(isInner && data.innerAdjustmentPower
        ? [
            {
              name: '月度双边调整',
              type: 'bar',
              yAxisIndex: 0,
              stack: 'power',
              data: data.innerAdjustmentPower,
              barMaxWidth: 30,
              itemStyle: {
                color: UNIT_COLORS.adjustmentPower,
              },
            },
          ]
        : []),

      // 外部交易 - 电量数据（柱状图）
      ...(!isInner && data.extraListingPower
        ? [
            {
              name: '挂牌电量',
              type: 'bar',
              yAxisIndex: 0,
              stack: 'power',
              data: data.extraListingPower,
              barMaxWidth: 30,
              itemStyle: {
                color: UNIT_COLORS.listingPower,
              },
            },
          ]
        : []),
      ...(!isInner && data.extraBiddingPower
        ? [
            {
              name: '竞价电量',
              type: 'bar',
              yAxisIndex: 0,
              stack: 'power',
              data: data.extraBiddingPower,
              barMaxWidth: 30,
              itemStyle: {
                color: UNIT_COLORS.biddingPower,
              },
            },
          ]
        : []),
      ...(!isInner && data.extraMatchingPower
        ? [
            {
              name: '撮合电量',
              type: 'bar',
              yAxisIndex: 0,
              stack: 'power',
              data: data.extraMatchingPower,
              barMaxWidth: 30,
              itemStyle: {
                color: UNIT_COLORS.matchingPower,
              },
            },
          ]
        : []),

      // 内部交易 - 收益数据（折线图）
      ...(isInner && data.innerListingRevenue
        ? [
            {
              name: '挂牌收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.innerListingRevenue,
              lineStyle: {
                width: px(3),
              },
              itemStyle: {
                color: UNIT_COLORS.listingRevenue,
              },
            },
          ]
        : []),
      ...(isInner && data.innerMatchingRevenue
        ? [
            {
              name: '撮合收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.innerMatchingRevenue,
              lineStyle: {
                width: px(3),
              },
              itemStyle: {
                color: UNIT_COLORS.matchingRevenue,
              },
            },
          ]
        : []),
      ...(isInner && data.innerAdjustmentRevenue
        ? [
            {
              name: '调整收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.innerAdjustmentRevenue,
              lineStyle: {
                width: px(3),
              },
              itemStyle: {
                color: UNIT_COLORS.adjustmentRevenue,
              },
            },
          ]
        : []),
      ...(isInner && data.innerTotalRevenue
        ? [
            {
              name: '总收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.innerTotalRevenue,
              lineStyle: {
                width: px(4),
                type: 'dashed',
              },
              itemStyle: {
                color: UNIT_COLORS.totalRevenue,
              },
            },
          ]
        : []),

      // 外部交易 - 收益数据（折线图）
      ...(!isInner && data.extraListingRevenue
        ? [
            {
              name: '挂牌收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.extraListingRevenue,
              lineStyle: {
                width: px(3),
              },
              itemStyle: {
                color: UNIT_COLORS.listingRevenue,
              },
            },
          ]
        : []),
      ...(!isInner && data.extraBiddingRevenue
        ? [
            {
              name: '竞价收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.extraBiddingRevenue,
              lineStyle: {
                width: px(3),
              },
              itemStyle: {
                color: UNIT_COLORS.biddingRevenue,
              },
            },
          ]
        : []),
      ...(!isInner && data.extraMatchingRevenue
        ? [
            {
              name: '撮合收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.extraMatchingRevenue,
              lineStyle: {
                width: px(3),
              },
              itemStyle: {
                color: UNIT_COLORS.matchingRevenue,
              },
            },
          ]
        : []),
      ...(!isInner && data.extraTotalRevenue
        ? [
            {
              name: '总收益',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              symbol: 'circle',
              data: data.extraTotalRevenue,
              lineStyle: {
                width: px(4),
                type: 'dashed',
              },
              itemStyle: {
                color: UNIT_COLORS.totalRevenue,
              },
            },
          ]
        : []),
    ],
  };
};
