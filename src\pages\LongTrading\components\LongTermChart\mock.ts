// 24小时数据，每小时一个数据点
export const chartData = {
  timePoints: [
    '00',
    '01',
    '02',
    '03',
    '04',
    '05',
    '06',
    '07',
    '08',
    '09',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
    '19',
    '20',
    '21',
    '22',
    '23',
  ],
  // 电量数据（柱状图）
  governmentAuthorizedPower: [
    320, 332, 301, 334, 390, 330, 350, 360, 370, 380, 390, 400, 410, 420, 430,
    440, 450, 460, 470, 480, 490, 500, 510, 520,
  ],
  yinDongBilateralPower: [
    220, 182, 191, 234, 290, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420,
    430, 440, 450, 460, 470, 480, 490, 500, 510,
  ],
  yinDongBiddingPower: [
    150, 232, 201, 154, 190, 330, 340, 350, 360, 370, 380, 390, 400, 410, 420,
    430, 440, 450, 460, 470, 480, 490, 500, 510,
  ],
  bilateralTransactionPower: [
    120, 132, 101, 134, 90, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320,
    330, 340, 350, 360, 370, 380, 390, 400, 410,
  ],
  biddingTransactionPower: [
    420, 432, 401, 434, 490, 530, 540, 550, 560, 570, 580, 590, 600, 610, 620,
    630, 640, 650, 660, 670, 680, 690, 700, 710,
  ],
  listingTransactionPower: [
    620, 732, 701, 734, 890, 930, 940, 950, 960, 970, 980, 990, 1000, 1010,
    1020, 1030, 1040, 1050, 1060, 1070, 1080, 1090, 1100, 1110,
  ],
  matchingTransactionPower: [
    520, 632, 601, 634, 790, 830, 840, 850, 860, 870, 880, 890, 900, 910, 920,
    930, 940, 950, 960, 970, 980, 990, 1000, 1010,
  ],

  // 电价数据（折线图）
  dayAheadClearingPrice: [
    410, 420, 415, 430, 435, 440, 445, 450, 455, 460, 465, 470, 475, 480, 485,
    490, 495, 500, 505, 510, 515, 520, 525, 530,
  ],
  realTimeClearingPrice: [
    400, 410, 405, 420, 425, 430, 435, 440, 445, 450, 455, 460, 465, 470, 475,
    480, 485, 490, 495, 500, 505, 510, 515, 520,
  ],
  realTimeAveragePrice: [
    405, 415, 410, 425, 430, 435, 440, 445, 450, 455, 460, 465, 470, 475, 480,
    485, 490, 495, 500, 505, 510, 515, 520, 525,
  ],

  // 额外电量指标（折线图）
  totalMediumLongTermPower: [
    1020, 1132, 1101, 1134, 1290, 1330, 1340, 1350, 1360, 1370, 1380, 1390,
    1400, 1410, 1420, 1430, 1440, 1450, 1460, 1470, 1480, 1490, 1500, 1510,
  ],
  dayAheadClearingPower: [
    900, 932, 901, 934, 990, 1030, 1040, 1050, 1060, 1070, 1080, 1090, 1100,
    1110, 1120, 1130, 1140, 1150, 1160, 1170, 1180, 1190, 1200, 1210,
  ],
  realTimePowerUsage: [
    920, 950, 940, 960, 980, 990, 1000, 1010, 1020, 1030, 1040, 1050, 1060,
    1070, 1080, 1090, 1100, 1110, 1120, 1130, 1140, 1150, 1160, 1170,
  ],
  actualPowerUsage80Percent: [
    736, 760, 752, 768, 784, 792, 800, 808, 816, 824, 832, 840, 848, 856, 864,
    872, 880, 888, 896, 904, 912, 920, 928, 936,
  ],
};
