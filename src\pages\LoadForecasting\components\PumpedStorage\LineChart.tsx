import '@/assets/css/helpers.sass';
import services from '@/services/decisionSupport';
import px from '@/utils/px';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef, useState } from 'react';
import styles from './index.sass';

interface Props {
  selectedDate: dayjs.Dayjs;
}

const Price: React.FC<Props> = ({ selectedDate }) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>(null);
  const { setMaxStorage, setMinStorage } = useModel('forecast');
  const { getPumpedStorage } = services.loadForecastController;
  useEffect(() => {
    const dateStr = selectedDate.format('YYYY-MM-DD');
    getPumpedStorage({ dateStr }).then((res) => {
      setData(res.data);
      setMaxStorage(res.data.maxDiff);
      setMinStorage(res.data.minDiff);
    });
  }, [selectedDate]);
  useEffect(() => {
    if (!chartRef.current || !data) {
      return;
    }
    const chartInstance = echarts.init(chartRef.current);

    // 配置项和数据
    const options = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['D日抽蓄总出力', 'D-1日抽蓄总出力'],
        textStyle: { color: '#fff', fontSize: px(16) },
        left: 'center',
        right: 'center',
      },
      grid: {
        left: px(10),
        right: px(22),
        top: px(50),
        bottom: px(0),
        containLabel: true,
      },
      dataZoom: [
        {
          type: 'inside', // 或 'inside'
          start: 0, // 开始位置（0-100）
          end: 100,
        },
      ],

      xAxis: {
        boundaryGap: false,
        type: 'category',
        data: data.timeList,
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
          interval: 8.5,
          showMaxLabel: true,
        },
        axisTick: { show: true },
      },
      yAxis: {
        type: 'value',
        /*         name: data.unit,
        nameTextStyle: { color: '#fff', fontSize: px(16) }, */
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
          },
        },
      },
      series: [
        {
          name: 'D日抽蓄总出力',
          type: 'line',
          smooth: true,
          data: data.pumpedList1,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#ff1515',
          },
          lineStyle: {
            color: '#ff1515',
            width: px(3),
          },
        },
        {
          name: 'D-1日抽蓄总出力',
          type: 'line',
          smooth: true,
          data: data.pumpedList0,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#006ebf',
          },
          lineStyle: {
            color: '#006ebf',
            width: px(3),
          },
        },
      ],
    };
    chartInstance.setOption(options);
  }, [data]);

  return <div ref={chartRef} className={styles.price} />;
};

export default Price;
