import CustomModal from '@/components/CustomModal';
import { getThreeCurves } from '@/services/efficientControl';
import px from '@/utils/px';
import { useModel, useRequest } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import LineChart from './LineChart';
import Select from './Select';
import styles from './index.sass';
interface Prop {
  id?: number;
  open: boolean;
  onCancel: () => void;
}
const Index: React.FC<Prop> = (prop: Prop) => {
  const [loading, setLoading] = useState(false);
  const { id, open, onCancel } = prop;
  const handleOk = () => {};
  const [demandData, setDemandData] = useState();
  const [rate, setRate] = useState<number>();
  const { baselineType } = useModel('efficient');
  const { run } = useRequest(getThreeCurves, {
    manual: true,
    onSuccess: (data: any) => {
      setLoading(false);
      setDemandData(data);
      setRate(data.agvCompleteRate);
    },
  });
  useEffect(() => {
    if (id) {
      setLoading(true);
      run({
        scheduleId: id,
        baselineMethodString: baselineType,
      });
    }
  }, [id, baselineType]);
  return (
    <CustomModal
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          分时调控详情
          <Select />
        </div>
      }
      open={open}
      onOk={handleOk}
      footer={null}
      centered={true}
      onCancel={onCancel}
      width={px(1500)}
    >
      <Spin
        wrapperClassName={styles.spin}
        spinning={loading}
        tip="计算中，请稍等···"
      >
        <h1 className={styles.rate}>
          {rate && <>响应完成率：{Math.round(rate * 100)}%</>}
          {demandData?.showMaxAndMinDiff && (
            <>
              &nbsp;&nbsp;&nbsp;&nbsp;最小偏差：
              {demandData?.minDeviation}
              &nbsp;&nbsp;&nbsp;&nbsp;最大偏差：{demandData?.maxDeviation}
            </>
          )}
        </h1>

        <LineChart data={demandData} />
      </Spin>
    </CustomModal>
  );
};

export default Index;
