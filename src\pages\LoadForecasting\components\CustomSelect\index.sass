@import '@/assets/css/helpers.sass'
.box
    margin-right: px(10)
    :global
        .ant-select-single .ant-select-selector,
        .ant-select .ant-select-arrow,
        .ant-select-selection-item,
        .anticon
            color: white !important
            font-size: px(14)
        .ant-select-selector,.ant-select-single .ant-select-selector
            background: rgba(0,0,0,0) !important
            border: px(1) white solid
        .ant-select-selector
            padding: 0 px(15) !important
            height: px(36) !important
        .ant-select-selection-item,.ant-select-selection-placeholder
            color: white 
            font-size: px(14)
        .ant-select-selector .ant-select-selection-placeholder,.ant-select-selection-item
            line-height: px(35) !important
        .ant-select-arrow
            margin-top: px(12)
            top: 0