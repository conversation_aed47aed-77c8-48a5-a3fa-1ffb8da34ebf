import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { chartData } from './mock';
import { getOption } from './option';

interface BarChartProps {
  data?: any;
  loading?: boolean;
}
export default function ({ data, loading = false }: BarChartProps) {
  const container = useRef<HTMLDivElement>(null);
  const myChartRef = useRef<echarts.ECharts>();

  // 初始化图表
  useEffect(() => {
    if (container.current) {
      // 初始化ECharts实例
      if (!myChartRef.current) {
        const chart = echarts.init(container.current);
        myChartRef.current = chart;

        // 窗口大小变化时自动调整图表大小
        const handleResize = () => {
          chart.resize();
        };
        window.addEventListener('resize', handleResize);

        // 初始设置图表选项
        chart.setOption(getOption(chartData));
      }

      return () => {
        if (myChartRef.current) {
          window.removeEventListener('resize', () => {});
          myChartRef.current.dispose();
          myChartRef.current = undefined;
        }
      };
    }
  }, []);

  // 更新图表数据
  useEffect(() => {
    if (myChartRef.current) {
      if (loading) {
        myChartRef.current.showLoading();
      } else {
        myChartRef.current.hideLoading();

        // 如果有数据，则更新图表
        if (data) {
          myChartRef.current.setOption(getOption(data));
        } else {
          // 如果没有数据，则使用默认数据
          myChartRef.current.setOption(getOption(chartData));
        }
      }
    }
  }, [data, loading]);

  return <div className={styles.box} ref={container}></div>;
}
