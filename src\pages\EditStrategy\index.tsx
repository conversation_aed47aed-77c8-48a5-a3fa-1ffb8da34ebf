import { useState } from 'react';

import EditResource from './components/EditResource';
import EditValue from './components/EditValue';
import Submit from './components/Submit';
import styles from './index.sass';

const step = [
  {
    name: '删减资源点',
    icon: '/step1.png',
    activeIcon: '/step11.png',
  },
  {
    name: '修改各时刻报量',
    icon: '/step2.png',
    activeIcon: '/step22.png',
  },
];
const Index = () => {
  const [current, setCurrent] = useState(0);
  return (
    <div className={styles.box}>
      <div className={styles.step}>
        {step.map((item, index) => {
          return (
            <>
              <div className={styles.item}>
                {current === index ? (
                  <img
                    src={item.activeIcon}
                    className={styles.activeIcon}
                    alt=""
                  />
                ) : (
                  <img src={item.icon} className={styles.icon} alt="" />
                )}
                <div>{item.name}</div>
              </div>
              {index === 0 && <div className={styles.bar0}></div>}
              {/* {index === 1 && <div className={styles.bar1}></div>} */}
            </>
          );
        })}
      </div>
      {current === 0 && (
        <EditResource onClickNext={() => setCurrent(1)}></EditResource>
      )}
      {current === 1 && (
        <EditValue
          onClickNext={() => setCurrent(2)}
          onClickPrev={() => setCurrent(0)}
        ></EditValue>
      )}
      {current === 2 && <Submit></Submit>}
    </div>
  );
};

export default Index;
