import CustomButton from '@/components/CustomButton';
import CustomDateRange from '@/components/CustomDateRange';
import { longRender } from '@/components/CustomLongRender';
import CustomSelect from '@/components/CustomSelect';
import CustomTable from '@/components/CustomTable';
import {
  AGetResourceType,
  getResourceSchedulepage,
} from '@/services/efficientControl';
import px from '@/utils/px';
import { datetimeFormat } from '@/utils/util';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { useAntdTable } from 'ahooks';
import { Form, TimeRangePickerProps, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { IResourceSchedule } from '../../typing';
import CustomModal from '../CustomModal';
import DemandDetail from './DemandDetail';
import ScheduleDetail from './ScheduleDetail';
import styles from './index.sass';
interface Props {
  name?: string;
}

const Index: React.FC<Props> = (props: Props) => {
  const { type, setType } = useModel('efficient');
  const {} = props;
  const container = useRef<HTMLDivElement>(null);
  const [open, setOpen] = useState(false);
  const [openDemand, setOpenDemand] = useState(false);
  const [id, setId] = useState<number>();
  const [openScheduleDetail, setOpenScheduleDetail] = useState(false);
  const [scheduleData, setScheduleData] = useState<IResourceSchedule>();
  const [allResourceType, setAllResourceType] = useState<string[]>([]);
  const [form] = Form.useForm();
  const rangePresets: TimeRangePickerProps['presets'] = [
    { label: '7天前', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '14天前', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '30天前', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '90天前', value: [dayjs().add(-90, 'd'), dayjs()] },
  ];
  const onType = (value: string) => {
    setType(value);
  };
  const { unitType } = useModel('unitType');
  interface Result {
    total: number;
    list: IResourceSchedule[];
  }

  interface Query {
    current: number;
    type: string;
    startTime?: string;
    endTime?: string;
    timeRange?: Dayjs[];
  }
  const getTableData = (
    { current }: { current: number },
    formData: object,
  ): Promise<Result> => {
    const query: Query = { current, type };
    Object.entries(formData).forEach(([key, value]) => {
      if (value) {
        if (key === 'timeRange') {
          query.startTime = value[0].format(datetimeFormat);
          query.endTime = value[1].format(datetimeFormat);
        } else {
          query[key] = value;
        }
      }
    });
    return getResourceSchedulepage(query).then((res) => {
      if (res.data) {
        return {
          total: res.data.total,
          list: res.data.list,
        };
      } else {
        throw new Error('Data not available');
      }
    });
  };

  const { tableProps, search } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 9 }],
    refreshDeps: [type, unitType],
  });

  const { submit, reset } = search;

  useEffect(() => {
    AGetResourceType().then((res) => {
      setAllResourceType(res);
    });
  }, [unitType]);
  // const clickDetail = () => {
  //   setOpen(true);
  // };
  const clickScheduleDetail = (data: IResourceSchedule) => {
    if (type === '日前现货市场') {
      setOpenScheduleDetail(true);
      setScheduleData(data);
    } else {
      setOpenDemand(true);
      setId(Number(data.scheduleId));
    }
  };
  const onCancel = () => {
    setOpen(false);
    setOpenScheduleDetail(false);
  };

  const columns: ColumnsType<IResourceSchedule> = [
    {
      title: '执行策略名称',
      dataIndex: 'scheduleId',
      key: 'scheduleId',
      align: 'center',
      fixed: 'left',
      width: px(150),
      render: (value) => '执行策略-' + value,
    },
    {
      title: '所属调度计划',
      dataIndex: 'decisionId',
      key: 'decisionId',
      align: 'center',
      fixed: 'left',
      width: px(150),
      render: (value) => '调度计划-' + value,
    },
    {
      title: '资源点名称',
      dataIndex: 'resourceName',
      key: 'resourceName',
      fixed: 'left',
      width: px(150),
      render: longRender,
    },
    {
      title: '资源点类型',
      dataIndex: 'resourceType',
      key: 'resourceType',
    },
    {
      title: '开始执行时间',
      dataIndex: 'startTime',
      key: 'startTime',
      align: 'center',
    },
    {
      title: '预计结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      align: 'center',
    },
    {
      title: (
        <Tooltip
          title="完成率=(各时刻实际负荷削减量/各时刻计划负荷削减量)/总时刻"
          color="blue"
        >
          完成率&nbsp;
          <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'completeRate',
      key: 'completeRate',
      align: 'center',
      render: (value) => {
        return (value?.toFixed(2) || 0) + '%';
      },
    },
    {
      title: '进度',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
    },
    {
      title: '市场类型',
      dataIndex: 'transactionType',
      key: 'transactionType',
      align: 'center',
    },
    {
      title: (
        <Tooltip title="与电网的交易电量总和，包含买电和卖电" color="blue">
          交易电量/MWh&nbsp;
          <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'planPowerConsumption',
      key: 'planPowerConsumption',
      align: 'center',
    },
    {
      title: type === '日前现货市场' ? '收益/万元' : '收益/元',
      dataIndex: 'totalProfit',
      key: 'totalProfit',
      align: 'center',
    },
    {
      title: type === '日前现货市场' ? '成本/万元' : '成本/元',
      dataIndex: 'totalCost',
      key: 'totalCost',
      align: 'center',
    },
    {
      title: type === '日前现货市场' ? '负荷削减总量/MWh' : '响应量/kW',
      dataIndex: 'totalLoadCut',
      key: 'totalLoadCut',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: px(150),
      render: (value: null, row: IResourceSchedule) => {
        return (
          <div className={styles.operate}>
            <span onClick={() => clickScheduleDetail(row)}>分时调控详情</span>
            {/* <span onClick={() => clickDetail()}>执行详情</span> */}
          </div>
        );
      },
    },
  ];
  return (
    <div className={styles.box} ref={container}>
      <div className={styles.title}>
        <span className={styles.text}>{unitType}执行策略信息</span>
        <CustomSelect
          onChange={onType}
          options={[
            { value: '日前现货市场', label: '日前现货市场' },
            { value: '需求响应', label: '需求响应' },
            { value: '辅助服务', label: '辅助服务' },
          ]}
          defaultValue={type}
        />
      </div>
      <Form className={styles.form} form={form}>
        {/* <Form.Item name="resourceName" label="交易名称">
          <CustomInput className={styles.input} placeholder="请输入交易名称" />
        </Form.Item> */}
        <Form.Item name="resourceType" label="资源类型">
          <CustomSelect
            className={styles.select}
            allowClear
            options={allResourceType.map((item) => {
              return {
                label: item,
                value: item,
              };
            })}
          ></CustomSelect>
        </Form.Item>
        <Form.Item name="status" label="进度">
          <CustomSelect
            allowClear
            className={styles.select}
            options={[
              {
                label: '未开始',
                value: '未开始',
              },
              {
                label: '进行中',
                value: '进行中',
              },
              {
                label: '已完成',
                value: '已完成',
              },
            ]}
          ></CustomSelect>
        </Form.Item>
        <Form.Item name="timeRange" label="时间范围">
          <CustomDateRange
            presets={rangePresets}
            className={styles.dateInput}
            showTime={false}
            format={'YYYY-MM-DD'}
          />
        </Form.Item>
        <Form.Item>
          <CustomButton className={styles.button} onClick={submit}>
            筛选
          </CustomButton>
        </Form.Item>
        <Form.Item>
          <CustomButton onClick={reset}>重置</CustomButton>
        </Form.Item>
      </Form>
      <div className={styles.table}>
        <CustomTable
          {...tableProps}
          scroll={{ x: px(2400) }}
          columns={columns}
        ></CustomTable>
      </div>
      <CustomModal open={open} onCancel={onCancel}></CustomModal>
      {scheduleData && (
        <ScheduleDetail
          scheduleData={scheduleData}
          open={openScheduleDetail}
          onCancel={onCancel}
        ></ScheduleDetail>
      )}
      <DemandDetail
        open={openDemand}
        onCancel={() => setOpenDemand(false)}
        id={id}
      />
    </div>
  );
};

export default Index;
