import EditingIcon from '@/assets/bg/EditStategy/Edited.svg';
import React from 'react';
import EditTitle from '../EditValue/EditTitle';
import InfoContent from '../EditValue/InfoContent';
import DeclaredChart from './DeclaredChart';
import styles from './index.sass';
interface Props {
  name?: string;
}

const Index: React.FC<Props> = () => {
  return (
    <div className={styles.box}>
      <div className={styles.displayChart}>
        <div className={styles.title}>
          <EditTitle
            title="售电曲线"
            info={
              <InfoContent
                status="当前状态："
                statusIcon={<img src={EditingIcon} />}
                infoName=" 已申报"
              />
            }
          />
        </div>
        <div className={styles.chart}>
          <DeclaredChart
            xAxisData={['01:00', '03:00', '05:00', '07:00', '09:00', '11:00']}
            seriesDataArrays={[
              [-230, -100, 800, 600, 100, 200],
              [-180, -50, 600, 300, 120, 230],
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default Index;
