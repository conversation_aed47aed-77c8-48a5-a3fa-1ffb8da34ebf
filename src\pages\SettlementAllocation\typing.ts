export interface IBenefitList {
  total: number;
  current: number;
  pageSize: number;
  list: IBenefitData[];
}
export interface IBenefitData {
  dayStr: string;
  profitId: number;
  winbidId: number;
  profitValue: number;
  time: string;
  income: number;
  cost: number;
  name: string;
}
export interface IBenefitDetailList {
  list: IBenefitDetailData[];
  [key: string]: any;
}
export interface IBenefitDetailData {
  actualCost: number;
  actualIncome: number;
  actualProfit: number;
  resourceName: string;
  startTime: string;
  endTime: string;
}
export interface IResourceBenefitList {
  list: IResourceBenefitData[];
  [key: string]: any;
}
export interface IResourceBenefitData {
  resourceId: string;
  resourceName: string;
  totalCost: string;
  totalLoadCut: string;
  totalProfit: string;
  type: string;
}
export interface IResourceBenefitDetailList {
  list: IResourceBenefitDetailData[];
  [key: string]: any;
}
export interface IResourceBenefitDetailData {
  time: string;
  totalCost: string;
  totalLoadCut: string;
  totalProfit: string;
}

export interface IUserBenefitList {
  list: IResourceBenefitData[];
  [key: string]: any;
}
export interface IUserBenefitData {
  resourceUserId: string;
  resourceUserName: string;
  totalCost: string;
  totalProfit: string;
  totalLoadCut: string;
}
export interface IUserBenefitDetailList {
  list: IResourceBenefitDetailData[];
  [key: string]: any;
}
export interface IUserBenefitDetailData {
  time: string;
  totalCost: string;
  totalLoadCut: string;
  totalProfit: string;
}
export interface IUserRankData {
  resourceUserName: string;
  totalProfit: string;
}
export interface IStatisticData {
  resourceTypeAndProfitRankList: IResourceTypeBenefit[];
  resourceUserProfitRankList: IUserRankData[];
}
export interface IResourceTypeBenefit {
  resourceType: string;
  profit: string;
}
export enum TRADE_TYPE {
  auxiliary = '辅助服务',
  demand = '需求响应',
  daytrade = '日前市场',
}
