import px from '@/utils/px';
import { chartData } from './mock';

export interface IElectricityData {
  timePoints: string[];
  // 电量数据（柱状图）
  governmentAuthorizedPower: number[];
  yinDongBilateralPower: number[];
  yinDongBiddingPower: number[];
  bilateralTransactionPower: number[];
  biddingTransactionPower: number[];
  listingTransactionPower: number[];
  matchingTransactionPower: number[];

  // 电价数据（折线图）
  dayAheadClearingPrice: number[];
  realTimeClearingPrice: number[];
  realTimeAveragePrice: number[];

  // 额外电量指标（折线图）
  totalMediumLongTermPower: number[];
  dayAheadClearingPower: number[];
  realTimePowerUsage: number[];
  actualPowerUsage80Percent: number[];

  // 新增字段
  actualElectricityConsumption: number[]; // 实际用电量
  coverageRate: number[]; // 覆盖率（百分比）
}

export const getOption = (data: IElectricityData = chartData) => {
  // 1. 收集所有电量（yAxisIndex:0）和电价（yAxisIndex:1）数据，过滤 null/undefined
  const powerSeries = [
    data.governmentAuthorizedPower,
    data.yinDongBilateralPower,
    data.yinDongBiddingPower,
    data.bilateralTransactionPower,
    data.biddingTransactionPower,
    data.listingTransactionPower,
    data.matchingTransactionPower,
    data.totalMediumLongTermPower,
    data.dayAheadClearingPower,
    data.realTimePowerUsage,
    data.actualPowerUsage80Percent,
    data.actualElectricityConsumption, // 新增实际用电量进power系列，便于y轴范围计算
  ];
  const priceSeries = [
    data.dayAheadClearingPrice,
    data.realTimeClearingPrice,
    data.realTimeAveragePrice,
  ];

  const flatten = (arr) =>
    arr.flat().filter((v) => v !== null && v !== undefined);
  const powerData = flatten(powerSeries);
  const priceData = flatten(priceSeries);

  let powerMin = 0,
    powerMax = 0,
    priceMin = 0,
    priceMax = 0;
  if (powerData.length > 0) {
    powerMin = Math.min(...powerData);
    powerMax = Math.max(...powerData);
  }
  if (priceData.length > 0) {
    priceMin = Math.min(...priceData);
    priceMax = Math.max(...priceData);
  }

  // 判断是否所有数据都在同一侧
  const allPowerPositive = powerMin >= 0;
  const allPowerNegative = powerMax <= 0;
  const allPricePositive = priceMin >= 0;
  const allPriceNegative = priceMax <= 0;
  let y1Min, y1Max, y2Min, y2Max;
  const padding = 1.1;

  if (
    (allPowerPositive && allPricePositive) ||
    (allPowerNegative && allPriceNegative)
  ) {
    // 两组都为正或都为负
    y1Min = 0;
    y2Min = 0;
    y1Max = Math.max(powerMax, priceMax);
    y2Max = Math.max(powerMax, priceMax);
    if (allPowerNegative && allPriceNegative) {
      // 全负，max=0
      y1Max = 0;
      y2Max = 0;
      y1Min = Math.min(powerMin, priceMin);
      y2Min = Math.min(powerMin, priceMin);
    }
    y1Max = y1Max * padding;
    y2Max = y2Max * padding;
    y1Min = y1Min * padding;
    y2Min = y2Min * padding;
  } else {
    // 跨零，采用比例对齐算法
    powerMin = Math.min(powerMin, 0);
    priceMin = Math.min(priceMin, 0);
    const powerRange = powerMax - powerMin;
    const priceRange = priceMax - priceMin;
    const ratio = priceRange === 0 ? 1 : powerRange / priceRange;
    if (powerMax < priceMax * ratio) {
      y1Max = priceMax * ratio;
      y2Max = priceMax;
    } else {
      y1Max = powerMax;
      y2Max = powerMax / ratio;
    }
    if (powerMin < priceMin * ratio) {
      y1Min = powerMin;
      y2Min = powerMin / ratio;
    } else {
      y1Min = priceMin * ratio;
      y2Min = priceMin;
    }
    y1Min = Math.min(y1Min, 0);
    y2Min = Math.min(y2Min, 0);
    y1Max = y1Max * padding;
    y2Max = y2Max * padding;
    y1Min = y1Min * padding;
    y2Min = y2Min * padding;
  }

  return {
    title: {
      text: '',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: (params: any[]) => {
        const arr = Array.isArray(params) ? params : [params];
        let result = arr[0]?.axisValueLabel
          ? arr[0].axisValueLabel + '<br/>'
          : '';
        result += arr
          .map((item) => {
            if (item.seriesName === '覆盖率') {
              return `${item.marker}${item.seriesName}：${item.value}%`;
            } else {
              return `${item.marker}${item.seriesName}：${item.value}`;
            }
          })
          .join('<br/>');
        return result;
      },
    },
    legend: {
      type: 'plain',
      width: '80%',
      textStyle: {
        color: 'white',
        fontSize: px(14),
      },
      data: [
        '政府授权电量',
        '银东双边电量',
        '银东竞价电量',
        '双边成交电量',
        '竞价成交电量',
        '挂牌成交电量',
        '撮合成交电量',
        '日前出清电价',
        '实时出清电价',
        '实时算数平均电价',
        '总中长期电量',
        '日前出清电量',
        '实时用电量',
        '80%实际用电量',
        '实际用电量',
      ],
    },

    grid: {
      left: '2%',
      right: '2%',
      bottom: '2%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: '10%',
      data: data.timePoints,
      axisLabel: {
        color: 'white',
        fontSize: px(14),
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '电量 (MWh)',
        position: 'left',
        min: y1Min,
        max: y1Max,
        axisLabel: {
          color: 'white',
          fontSize: px(14),
          formatter: (value: number) => {
            if (Number.isInteger(value)) {
              return value;
            } else {
              return value.toFixed(2);
            }
          },
        },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLine: {
          lineStyle: {
            color: '#5470C6',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.17)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '电价 (元/MWh)',
        position: 'right',
        min: y2Min,
        max: y2Max,
        axisLabel: {
          color: 'white',
          fontSize: px(14),
          formatter: (value: number) => {
            if (Number.isInteger(value)) {
              return value;
            } else {
              return value.toFixed(2);
            }
          },
        },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLine: {
          lineStyle: {
            color: '#EE6666',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '覆盖率(%)',
        position: 'right',
        offset: 60,
        min: 0,
        max: 100,
        show: false, // 不显示覆盖率y轴
        axisLabel: {
          color: 'white',
          fontSize: px(14),
          formatter: '{value}%',
        },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLine: {
          lineStyle: { color: '#00c2b3' },
        },
        splitLine: { show: false },
      },
    ],

    series: [
      // 实际用电量柱体（重叠显示在所有电量柱最上方）
      {
        name: '实际用电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '1', // 参与主堆叠
        // barGap: '-100%', // 完全覆盖堆叠柱
        barWidth: px(20), // 与堆叠柱一致

        itemStyle: {
          color: 'rgba(136, 255, 245, 0.73)', // 高亮青色
        },
        data: data.actualElectricityConsumption,
      },
      // 覆盖率折线（只在tooltip显示，图上不显示）
      {
        name: '覆盖率',
        type: 'line',
        yAxisIndex: 2,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: px(3),
          color: '#00c2b3',
          opacity: 0, // 不显示线
        },
        itemStyle: {
          color: '#00c2b3',
          opacity: 0, // 不显示点
        },
        label: {
          show: false, // 不显示label
        },
        data: data.coverageRate,
      },
      // 柱状图：电量数据
      {
        name: '政府授权电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '0',
        barWidth: px(20),
        barGap: '0%',
        barCategoryGap: '50%',
        itemStyle: {
          color: '#4169E1', // Royal Blue
        },
        data: data.governmentAuthorizedPower,
      },
      {
        name: '银东双边电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '0',
        barWidth: px(20),
        barGap: '0%',
        barCategoryGap: '50%',
        itemStyle: {
          color: '#1E90FF', // Dodger Blue
        },
        data: data.yinDongBilateralPower,
      },
      {
        name: '银东竞价电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '0',
        barWidth: px(20),
        barGap: '0%',
        barCategoryGap: '50%',
        itemStyle: {
          color: '#00BFFF', // Deep Sky Blue
        },
        data: data.yinDongBiddingPower,
      },
      {
        name: '双边成交电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '0',
        barWidth: px(20),
        barGap: '0%',
        barCategoryGap: '50%',
        itemStyle: {
          color: '#32CD32', // Lime Green
        },
        data: data.bilateralTransactionPower,
      },
      {
        name: '竞价成交电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '0',
        barWidth: px(20),
        barGap: '0%',
        barCategoryGap: '50%',
        itemStyle: {
          color: '#FFD700', // Gold
        },
        data: data.biddingTransactionPower,
      },
      {
        name: '挂牌成交电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '0',
        barWidth: px(20),
        barGap: '0%',
        barCategoryGap: '50%',
        itemStyle: {
          color: '#FF8C00', // Dark Orange
        },
        data: data.listingTransactionPower,
      },
      {
        name: '撮合成交电量',
        type: 'bar',
        yAxisIndex: 0,
        stack: '0',
        barWidth: px(20),
        barGap: '0%',
        barCategoryGap: '50%',
        itemStyle: {
          color: '#FF4500', // Orange Red
        },
        data: data.matchingTransactionPower,
      },

      // 折线图：电价相关
      {
        name: '日前出清电价',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        lineStyle: {
          width: px(4),
          color: '#CC0000',
        },
        itemStyle: {
          color: '#CC0000',
        },
        data: data.dayAheadClearingPrice,
      },
      {
        name: '实时出清电价',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        lineStyle: {
          width: px(4),
          color: '#0000CC',
        },
        itemStyle: {
          color: '#0000CC',
        },
        data: data.realTimeClearingPrice,
      },
      // {
      //   name: '实时算数平均电价',
      //   type: 'line',
      //   yAxisIndex: 1,
      //   smooth: true,
      //   lineStyle: {
      //     width: px(4),
      //     color: '#009900',
      //   },
      //   itemStyle: {
      //     color: '#009900',
      //   },
      //   symbol: 'circle',
      //   data: data.realTimeAveragePrice,
      // },

      // 折线图：电量额外指标
      {
        name: '总中长期电量',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        lineStyle: {
          width: px(4),
          color: '#660066',
        },
        itemStyle: {
          color: '#660066',
        },
        symbol: 'circle',
        data: data.totalMediumLongTermPower,
      },
      {
        name: '日前出清电量',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        symbol: 'circle',
        lineStyle: {
          width: px(4),
          color: '#996600',
        },
        itemStyle: {
          color: '#996600',
        },
        data: data.dayAheadClearingPower,
      },
      {
        name: '实时用电量',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        lineStyle: {
          width: px(4),
          color: '#CC3300',
        },
        itemStyle: {
          color: '#CC3300',
        },
        data: data.realTimePowerUsage,
      },
      {
        name: '80%实际用电量',
        type: 'line',
        yAxisIndex: 0,
        smooth: true,
        symbol: 'circle',
        lineStyle: {
          type: 'dashed',
          width: px(4),
          color: '#006666',
        },
        itemStyle: {
          color: '#006666',
        },
        data: data.actualPowerUsage80Percent,
      },
    ],
  };
};
