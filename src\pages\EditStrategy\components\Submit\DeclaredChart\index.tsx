// import { MicroPowerContext } from '@/pages/UserDetail/components/PowerInfo/MicroPowerContext';
import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect } from 'react';
const DeclaredChart = ({ xAxisData, seriesDataArrays }) => {
  // const {startTime, endTime} = useContext(MicroPowerContext);
  useEffect(() => {
    const chartDom = document.getElementById('stacked-line-chart');
    const myChart = echarts.init(chartDom);

    const option = {
      color: ['#0077FF', '#E7FFFF'],
      title: {
        text: ' ',
        textStyle: {
          color: '#fff', // Title text color
          fontSize: px(10),
        },
      },
      dataZoom: [
        {
          type: 'inside', // 或 'inside'
          start: 0, // 开始位置（0-100）
          end: 100,
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        // formatter: function (params) {
        //   return params
        //     .map((param) => {
        //       let value = param.value;
        //       let seriesName = param.seriesName;

        //       // Add unit based on series name
        //       let unit = '';
        //       if (seriesName === '氢气') unit = ' unit (log2)'; // Replace 'unit' with actual unit
        //       if (seriesName === '甲醛') unit = ' unit'; // Replace 'unit' with actual unit
        //       if (seriesName === '乙醇') unit = ' unit (log2)'; // Indicate logarithmic value
        //       if (seriesName === '二氧化碳') unit = ' unit (log2)'; // Replace 'unit' with actual unit

        //       return `${seriesName}: ${value}${unit}`;
        //     })
        //     .join('<br/>');
        // },
      },
      legend: {
        left: 'right', // 图例居中
        top: '3%',
        data: ['算法生成策略', '已申报策略'],
        textStyle: {
          color: '#fff', // Title text color
          fontSize: px(14), // 将字体大小调整
        },
      },
      toolbox: {
        // feature: {
        //   saveAsImage: {},
        // },
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: {
            color: 'rgba(216, 240, 255, 0.8)', // X axis labels color
            fontSize: px(12),
            interval: 0,
          },
        },
      ],
      yAxis: [
        {
          name: 'MW',
          type: 'value',
          axisLabel: {
            color: 'rgba(216, 240, 255, 0.8)', // Y axis labels color
            fontSize: px(12), // 减小Y轴标签字体大小至8px
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#304A65',
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        // Add your series data here, following the format of the first series
        // Example for one series:
        {
          name: '算法生成策略',
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: px(3),
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgb(61, 228, 255)' }, // 渐变起始颜色
              { offset: 1, color: 'rgb(25, 106, 255)' }, // 渐变结束颜色
            ]),
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0.2,
                color: 'rgba(0, 115, 249, 0.51)',
              },
              {
                offset: 0.6,
                color: 'rgba(98, 156, 223, 0.47)',
              },

              {
                offset: 0.8,
                color: 'rgba(196, 196, 196, 0)',
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
          data: seriesDataArrays[0],
        },

        {
          name: '已申报策略',
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: px(4),
            color: '#E7FFFF',
            shadowColor: 'rgba(0, 220, 255, 1)', // 阴影颜色
            shadowBlur: 500, // 阴影的模糊级别
            shadowOffsetX: -20, // 阴影在X轴的偏移
            shadowOffsetY: -20, // 阴影在Y轴的偏移
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0.2,
                color: 'rgba(98, 156, 223, 0.47)',
              },

              {
                offset: 0.8,
                color: 'rgba(196, 196, 196, 0)',
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
          data: seriesDataArrays[1],
        },

        // Repeat for each line (Line 2, Line 3, etc.)
      ],
    };

    myChart.setOption(option);

    return () => {
      myChart.dispose();
    };
  }, [xAxisData, seriesDataArrays]);

  return (
    <div
      id="stacked-line-chart"
      style={{ height: px(580), width: '100%' }}
    ></div>
  );
};

export default DeclaredChart;
