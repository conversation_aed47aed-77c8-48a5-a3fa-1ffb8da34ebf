import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { chartData } from './mock';
import { IRevenueData, getOption } from './option';

interface BarLineChartProps {
  data?: any;
  type: 'inner' | 'extra';
  loading?: boolean;
}
export default function ({ data, type, loading = false }: BarLineChartProps) {
  const container = useRef<HTMLDivElement>(null);
  const myChartRef = useRef<echarts.ECharts>();

  // 初始化图表
  useEffect(() => {
    if (container.current) {
      // 初始化ECharts实例
      if (!myChartRef.current) {
        const chart = echarts.init(container.current);
        myChartRef.current = chart;

        // 窗口大小变化时自动调整图表大小
        const handleResize = () => {
          chart.resize();
        };
        window.addEventListener('resize', handleResize);

        // 初始设置图表选项
        chart.setOption(getOption(chartData));
      }

      return () => {
        if (myChartRef.current) {
          window.removeEventListener('resize', () => {});
          myChartRef.current.dispose();
          myChartRef.current = undefined;
        }
      };
    }
  }, []);

  // 更新图表数据
  useEffect(() => {
    if (myChartRef.current) {
      if (loading) {
        myChartRef.current.showLoading();
      } else {
        myChartRef.current.hideLoading();

        // 如果有数据，则更新图表
        if (data) {
          // 直接传递原始数据和类型
          const chartData: IRevenueData = {
            ...data,
            type, // 设置图表类型（内部或外部）
          };

          myChartRef.current.setOption(getOption(chartData));
        } else {
          // 如果没有数据，则使用默认数据
          const emptyData: IRevenueData = {
            timePoints: [],
            type,
            innerListingPower: type === 'inner' ? [] : undefined,
            innerMatchingPower: type === 'inner' ? [] : undefined,
            innerAdjustmentPower: type === 'inner' ? [] : undefined,
            innerListingRevenue: type === 'inner' ? [] : undefined,
            innerMatchingRevenue: type === 'inner' ? [] : undefined,
            innerAdjustmentRevenue: type === 'inner' ? [] : undefined,
            innerTotalRevenue: type === 'inner' ? [] : undefined,
            extraListingPower: type === 'extra' ? [] : undefined,
            extraBiddingPower: type === 'extra' ? [] : undefined,
            extraMatchingPower: type === 'extra' ? [] : undefined,
            extraListingRevenue: type === 'extra' ? [] : undefined,
            extraBiddingRevenue: type === 'extra' ? [] : undefined,
            extraMatchingRevenue: type === 'extra' ? [] : undefined,
            extraTotalRevenue: type === 'extra' ? [] : undefined,
          };
          myChartRef.current.setOption(getOption(emptyData));
        }
      }
    }
  }, [data, type, loading]);

  return <div className={styles.box} ref={container}></div>;
}
