import CustomModal from '@/components/CustomModal';
import CustomTab from '@/components/CustomTab';
import {
  IResourceSchedule,
  IScheduleDetail,
} from '@/pages/EfficientControl/typing';
import { AGetScheduleDetail } from '@/services/efficientControl';
import px from '@/utils/px';
import { Spin, TabsProps } from 'antd';
import React, { useEffect, useState } from 'react';
import ChargeChart from '../DetailCharts/ChargeChart';
import LoadChart from '../DetailCharts/LoadChart';
import ReserveChart from '../DetailCharts/ReserveChart';
import TogetherChart from '../DetailCharts/TogetherChart';
import styles from './index.sass';

interface Prop {
  open: boolean;
  scheduleData: IResourceSchedule;
  onCancel: () => void;
}
const Index: React.FC<Prop> = (prop: Prop) => {
  const { open, onCancel, scheduleData } = prop;
  const [loading, setLoading] = useState(false);

  const [scheduleDetail, setScheduleDetail] = useState<IScheduleDetail>();
  useEffect(() => {
    if (scheduleData) {
      setLoading(true);
      AGetScheduleDetail({ id: scheduleData.scheduleId }).then((res) => {
        setScheduleDetail(res);
        setLoading(false);
      });
    }
  }, [scheduleData]);
  const handleOk = () => {};
  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '收益情况曲线',
      children: (
        <TogetherChart scheduleDetail={scheduleDetail as IScheduleDetail} />
      ),
    },
    {
      key: '2',
      label: '负荷削减量曲线',
      children: (
        <LoadChart scheduleDetail={scheduleDetail as IScheduleDetail} />
      ),
    },
    {
      key: '3',
      label: '充放电曲线',
      children: (
        <ChargeChart scheduleDetail={scheduleDetail as IScheduleDetail} />
      ),
    },
    {
      key: '4',
      label: '储备电量曲线',
      children: (
        <ReserveChart scheduleDetail={scheduleDetail as IScheduleDetail} />
      ),
    },
  ];
  if (scheduleData.resourceType === '充电桩') {
    items.splice(2, 2);
  } else {
    items.splice(1, 1);
    items.splice(2, 1);
  }
  if (!scheduleDetail) return null;
  return (
    <CustomModal
      title="分时调控详情"
      open={open}
      onOk={handleOk}
      footer={null}
      centered={true}
      onCancel={onCancel}
      width={px(1200)}
    >
      <Spin
        wrapperClassName={styles.spin}
        spinning={loading}
        tip="计算中，请稍等···"
      >
        <CustomTab defaultActiveKey="1" items={items} />
      </Spin>
    </CustomModal>
  );
};

export default Index;
