import React from 'react';
import styles from './index.sass';

interface EditTitleProps {
  title: string;
  info: React.ReactNode; // 将类型从string更改为React.ReactNode
}

const EditTitle: React.FC<EditTitleProps> = ({ title, info }) => {
  return (
    <div className={styles.container}>
      <div className={styles.textarea}>{title}</div>
      <div className={styles.detail}>{info}</div>
    </div>
  );
};

export default EditTitle;
