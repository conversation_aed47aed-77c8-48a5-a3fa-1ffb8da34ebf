@import '@/assets/css/helpers.sass'

.container
  color: aliceblue
  padding: px(20)
  width: 100%
  height: 100%
  display: flex
  flex-direction: column

.timePicker
  margin-bottom: px(10)

.gridLayout
  flex: 1
  display: grid
  grid-template-columns: repeat(3, 1fr)
  grid-template-rows: repeat(2, 1fr)
  gap: px(20)
  min-height: 0

.gridItem
  background: rgba(255, 255, 255, 0.05)
  border: 1px solid rgba(255, 255, 255, 0.1)
  border-radius: px(8)
  padding: px(10)
  display: flex
  flex-direction: column
  min-height: 0

