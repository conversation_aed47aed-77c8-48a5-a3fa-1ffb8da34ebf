import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import { AGetDailyProgress } from '@/services/settlementAllocation';
import px from '@/utils/px.js';
import { Steps } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { IBenefitData } from '../../../typing';
import styles from './index.sass';

interface Props {
  open: boolean;
  onCancel: () => void;
  benefitData: IBenefitData;
}

const Index = (props: Props) => {
  const { open, onCancel, benefitData } = props;
  console.log('benifitData', benefitData);
  // 步骤条状态
  const [currentStep, setCurrentStep] = useState(0);
  // 滚动位置保留
  const [scrollPositions, setScrollPositions] = useState<{
    [key: number]: number;
  }>({});
  const scrollContainerRefs = {
    0: null as HTMLDivElement | null,
    1: null as HTMLDivElement | null,
    2: null as HTMLDivElement | null,
    3: null as HTMLDivElement | null,
  };

  // 保存当前步骤的滚动位置
  const saveScrollPosition = (stepIndex: number) => {
    const container = scrollContainerRefs[stepIndex];
    if (container) {
      setScrollPositions((prev) => ({
        ...prev,
        [stepIndex]: container.scrollTop,
      }));
    }
  };

  // 恢复滚动位置
  const restoreScrollPosition = (stepIndex: number) => {
    const container = scrollContainerRefs[stepIndex];
    const savedPosition = scrollPositions[stepIndex];
    if (container && savedPosition !== undefined) {
      setTimeout(() => {
        container.scrollTop = savedPosition;
      }, 0);
    }
  };

  // 步骤切换时保存当前滚动位置
  const handleStepChange = (newStep: number) => {
    saveScrollPosition(currentStep);
    setCurrentStep(newStep);
  };

  useEffect(() => {
    // 恢复新步骤的滚动位置
    restoreScrollPosition(currentStep);
  }, [currentStep]);

  useEffect(() => {
    AGetDailyProgress({ dayStr: benefitData.dayStr }).then((res) => {
      console.log('res', res);
    });
  }, [benefitData.dayStr]);
  // 步骤配置
  const steps = [
    {
      title: '调节量清单',
      description: '每15分钟调节量及清单详情',
    },
    {
      title: '开停机信息',
      description: '开停机状态与电价信息',
    },
    {
      title: '收益计算',
      description: '每小时调节量与收益计算',
    },
    {
      title: '收益统计',
      description: '今日整体收益统计信息',
    },
  ];

  const columns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'dateStr',
      key: 'dateStr',
      width: 100,
      align: 'center',
    },
    {
      title: '整体基线(kW)',
      dataIndex: 'wholeBaseline',
      key: 'wholeBaseline',
      width: 120,
      align: 'center',
    },
    {
      title: '整体调节量(kW)',
      dataIndex: 'wholeAdjust',
      key: 'wholeAdjust',
      width: 130,
      align: 'center',
    },
    {
      title: '特来电基线(kW)',
      dataIndex: 'teldBaseline',
      key: 'teldBaseline',
      width: 130,
      align: 'center',
    },
    {
      title: '特来电调节量(kW)',
      dataIndex: 'teldAdjust',
      key: 'teldAdjust',
      width: 140,
      align: 'center',
    },
    {
      title: '电表功率(kW)',
      dataIndex: 'meterPower',
      key: 'meterPower',
      width: 120,
      align: 'center',
    },
    {
      title: '电表基线(kW)',
      dataIndex: 'meterBaseline',
      key: 'meterBaseline',
      width: 120,
      align: 'center',
    },
    {
      title: '电表调节量(kW)',
      dataIndex: 'meterAdjust',
      key: 'meterAdjust',
      width: 130,
      align: 'center',
    },
  ];

  // 第二步：开停机信息表格列配置
  const operationColumns: ColumnsType<any> = [
    {
      title: '时间段',
      dataIndex: 'timeRange',
      key: 'timeRange',
      align: 'center',
      width: px(150),
    },
    {
      title: '开停机状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: px(120),
      render: (status: string) => (
        <span
          style={{
            color: status === '开机' ? '#52C41A' : '#FF4D4F',
            fontWeight: 'bold',
          }}
        >
          {status}
        </span>
      ),
    },
    {
      title: '电价(元/kWh)',
      dataIndex: 'price',
      key: 'price',
      align: 'center',
      width: px(120),
    },
    {
      title: '调节电价(元/kWh)',
      dataIndex: 'adjustPrice',
      key: 'adjustPrice',
      align: 'center',
      width: px(150),
    },
  ];

  // 第三步：收益计算表格列配置
  const profitColumns: ColumnsType<any> = [
    {
      title: '时间段',
      dataIndex: 'timeRange',
      key: 'timeRange',
      align: 'center',
      width: px(150),
    },
    {
      title: '调节量(kW)',
      dataIndex: 'adjustAmount',
      key: 'adjustAmount',
      align: 'center',
      width: px(120),
    },
    {
      title: '收益计算',
      dataIndex: 'profitCalc',
      key: 'profitCalc',
      align: 'center',
      width: px(200),
    },
    {
      title: '收益(元)',
      dataIndex: 'profit',
      key: 'profit',
      align: 'center',
      width: px(120),
      render: (profit: number) => (
        <span
          style={{
            color: profit > 0 ? '#52C41A' : '#666',
            fontWeight: 'bold',
          }}
        >
          {profit}
        </span>
      ),
    },
  ];

  // 第一步：15分钟调节量数据
  const adjustmentData = [
    { key: '1', startTime: '09:00', actualCost: '37.5' },
    { key: '2', startTime: '09:15', actualCost: '42.3' },
    { key: '3', startTime: '09:30', actualCost: '38.9' },
    { key: '4', startTime: '09:45', actualCost: '45.2' },
    { key: '5', startTime: '10:00', actualCost: '50.1' },
    { key: '6', startTime: '10:15', actualCost: '48.7' },
    { key: '7', startTime: '10:30', actualCost: '52.3' },
    { key: '8', startTime: '10:45', actualCost: '49.8' },
    { key: '9', startTime: '11:00', actualCost: '45.6' },
    { key: '10', startTime: '11:15', actualCost: '43.2' },
    { key: '11', startTime: '11:30', actualCost: '41.8' },
    { key: '12', startTime: '11:45', actualCost: '44.5' },
    { key: '13', startTime: '12:00', actualCost: '47.3' },
    { key: '14', startTime: '12:15', actualCost: '46.1' },
    { key: '15', startTime: '12:30', actualCost: '48.9' },
    { key: '16', startTime: '12:45', actualCost: '51.2' },
    { key: '17', startTime: '13:00', actualCost: '49.7' },
    { key: '18', startTime: '13:15', actualCost: '52.8' },
    { key: '19', startTime: '13:30', actualCost: '50.4' },
    { key: '20', startTime: '13:45', actualCost: '48.6' },
  ];

  // 模拟数据
  const operationData = [
    {
      key: '1',
      timeRange: '00:00-01:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '2',
      timeRange: '01:00-02:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '3',
      timeRange: '02:00-03:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '4',
      timeRange: '03:00-04:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '5',
      timeRange: '04:00-05:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '6',
      timeRange: '05:00-06:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '7',
      timeRange: '06:00-07:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '8',
      timeRange: '07:00-08:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '9',
      timeRange: '08:00-09:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '10',
      timeRange: '09:00-10:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '11',
      timeRange: '10:00-11:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '12',
      timeRange: '11:00-12:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '13',
      timeRange: '12:00-13:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '14',
      timeRange: '13:00-14:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '15',
      timeRange: '14:00-15:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '16',
      timeRange: '15:00-16:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '17',
      timeRange: '16:00-17:00',
      status: '开机',
      price: '0.65',
      adjustPrice: '0.85',
    },
    {
      key: '18',
      timeRange: '17:00-18:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '19',
      timeRange: '18:00-19:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '20',
      timeRange: '19:00-20:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '21',
      timeRange: '20:00-21:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '22',
      timeRange: '21:00-22:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '23',
      timeRange: '22:00-23:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
    {
      key: '24',
      timeRange: '23:00-24:00',
      status: '停机',
      price: '0.45',
      adjustPrice: '0.65',
    },
  ];

  const profitData = [
    {
      key: '1',
      timeRange: '00:00-01:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '2',
      timeRange: '01:00-02:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '3',
      timeRange: '02:00-03:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '4',
      timeRange: '03:00-04:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '5',
      timeRange: '04:00-05:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '6',
      timeRange: '05:00-06:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '7',
      timeRange: '06:00-07:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '8',
      timeRange: '07:00-08:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '9',
      timeRange: '08:00-09:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '10',
      timeRange: '09:00-10:00',
      adjustAmount: '150',
      profitCalc: '150 × 0.85 = 127.5',
      profit: 127.5,
    },
    {
      key: '11',
      timeRange: '10:00-11:00',
      adjustAmount: '200',
      profitCalc: '200 × 0.85 = 170',
      profit: 170,
    },
    {
      key: '12',
      timeRange: '11:00-12:00',
      adjustAmount: '180',
      profitCalc: '180 × 0.85 = 153',
      profit: 153,
    },
    {
      key: '13',
      timeRange: '12:00-13:00',
      adjustAmount: '220',
      profitCalc: '220 × 0.85 = 187',
      profit: 187,
    },
    {
      key: '14',
      timeRange: '13:00-14:00',
      adjustAmount: '195',
      profitCalc: '195 × 0.85 = 165.75',
      profit: 165.75,
    },
    {
      key: '15',
      timeRange: '14:00-15:00',
      adjustAmount: '175',
      profitCalc: '175 × 0.85 = 148.75',
      profit: 148.75,
    },
    {
      key: '16',
      timeRange: '15:00-16:00',
      adjustAmount: '210',
      profitCalc: '210 × 0.85 = 178.5',
      profit: 178.5,
    },
    {
      key: '17',
      timeRange: '16:00-17:00',
      adjustAmount: '185',
      profitCalc: '185 × 0.85 = 157.25',
      profit: 157.25,
    },
    {
      key: '18',
      timeRange: '17:00-18:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '19',
      timeRange: '18:00-19:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '20',
      timeRange: '19:00-20:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '21',
      timeRange: '20:00-21:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '22',
      timeRange: '21:00-22:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '23',
      timeRange: '22:00-23:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
    {
      key: '24',
      timeRange: '23:00-24:00',
      adjustAmount: '0',
      profitCalc: '未开机',
      profit: 0,
    },
  ];

  // 第四步：收益统计表格列配置
  const statisticsColumns: ColumnsType<any> = [
    {
      title: '统计项目',
      dataIndex: 'item',
      key: 'item',
      width: 200,
      align: 'center',
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      width: 150,
      align: 'center',
      render: (value: string, record: any) => {
        const isImportant = ['总收益', '收益率', '总调节量'].includes(
          record.item,
        );
        return (
          <span
            style={{
              color: isImportant ? '#00D4FF' : '#94EFFF',
              fontWeight: isImportant ? 'bold' : 'normal',
              fontSize: isImportant ? '16px' : '14px',
            }}
          >
            {value}
          </span>
        );
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      align: 'center',
    },
  ];

  // 收益统计数据
  const statisticsData = [
    {
      key: '1',
      item: '总调节量',
      value: '1,520',
      unit: 'kW',
      remark: '全天累计',
    },
    {
      key: '2',
      item: '开机时段调节量',
      value: '1,520',
      unit: 'kW',
      remark: '09:00-17:00',
    },
    {
      key: '3',
      item: '停机时段调节量',
      value: '0',
      unit: 'kW',
      remark: '其他时段',
    },
    {
      key: '4',
      item: '总收益',
      value: '1,292.0',
      unit: '元',
      remark: '今日总计',
    },
    {
      key: '5',
      item: '平均每小时收益',
      value: '161.5',
      unit: '元/小时',
      remark: '开机时段平均',
    },
    {
      key: '6',
      item: '最高单小时收益',
      value: '187.0',
      unit: '元',
      remark: '12:00-13:00时段',
    },
    {
      key: '7',
      item: '最低单小时收益',
      value: '127.5',
      unit: '元',
      remark: '09:00-10:00时段',
    },
    {
      key: '8',
      item: '调节电价',
      value: '0.85',
      unit: '元/kWh',
      remark: '当日执行价格',
    },
    {
      key: '9',
      item: '基础电价',
      value: '0.65',
      unit: '元/kWh',
      remark: '当日执行价格',
    },
    {
      key: '10',
      item: '收益率',
      value: '30.8',
      unit: '%',
      remark: '相对基础电价',
    },
  ];

  // 根据当前步骤渲染不同内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>每15分钟调节量清单</h3>
            <div
              style={{
                width: '100%',
                minWidth: '500px',
                maxHeight: '700px',
                overflowY: 'auto',
              }}
            >
              <CustomTable
                dataSource={adjustmentData}
                columns={columns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      case 1:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>开停机与电价信息</h3>
            <div
              style={{
                width: '100%',
                minWidth: '600px',
                maxHeight: '400px',
                overflowY: 'auto',
              }}
            >
              <CustomTable
                dataSource={operationData}
                columns={operationColumns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>每小时调节量与收益计算</h3>
            <div
              style={{
                width: '100%',
                minWidth: '700px',
                maxHeight: '400px',
                overflowY: 'auto',
              }}
            >
              <CustomTable
                dataSource={profitData}
                columns={profitColumns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      case 3:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>今日整体收益统计</h3>
            <div
              style={{
                width: '100%',
                minWidth: '650px',
                maxHeight: '400px',
                overflowY: 'auto',
              }}
            >
              <CustomTable
                dataSource={statisticsData}
                columns={statisticsColumns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <CustomModal
      open={open}
      title="计算流程详情"
      onCancel={onCancel}
      footer={null}
      className={styles.box}
      width={px(1400)}
      centered={true}
    >
      {/* 步骤条 */}
      <div className={styles.stepsContainer}>
        <Steps
          current={currentStep}
          onChange={setCurrentStep}
          className={styles.customSteps}
          items={steps}
          size="small"
        />
      </div>

      {/* 步骤内容 */}
      {renderStepContent()}
    </CustomModal>
  );
};

export default Index;
