import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import { AGetDailyProgress } from '@/services/settlementAllocation';
import px from '@/utils/px.js';
import { Steps } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { IBenefitData } from '../../../typing';
import styles from './index.sass';

interface Props {
  open: boolean;
  onCancel: () => void;
  benefitData: IBenefitData;
}

const Index = (props: Props) => {
  const { open, onCancel, benefitData } = props;
  console.log('benifitData', benefitData);
  // 步骤条状态
  const [currentStep, setCurrentStep] = useState(0);
  // 接口数据状态
  const [apiData, setApiData] = useState<any>(null);

  useEffect(() => {
    AGetDailyProgress({ dayStr: benefitData.dayStr }).then((res: any) => {
      console.log('res', res);
      setApiData(res);
    });
  }, [benefitData.dayStr]);
  // 步骤配置
  const steps = [
    {
      title: '调节量清单',
      description: '每15分钟调节量及清单详情',
    },
    {
      title: '开停机信息',
      description: '开停机状态与电价信息',
    },
    {
      title: '收益计算',
      description: '每小时调节量与收益计算',
    },
    {
      title: '收益统计',
      description: '今日整体收益统计信息',
    },
  ];

  const columns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'dateStr',
      key: 'dateStr',
      width: 100,
      align: 'center',
    },
    {
      title: '整体基线(kW)',
      dataIndex: 'wholeBaseline',
      key: 'wholeBaseline',
      width: 120,
      align: 'center',
    },
    {
      title: '整体调节量(kW)',
      dataIndex: 'wholeAdjust',
      key: 'wholeAdjust',
      width: 130,
      align: 'center',
    },
    {
      title: '特来电基线(kW)',
      dataIndex: 'teldBaseline',
      key: 'teldBaseline',
      width: 130,
      align: 'center',
    },
    {
      title: '特来电调节量(kW)',
      dataIndex: 'teldAdjust',
      key: 'teldAdjust',
      width: 140,
      align: 'center',
    },
    {
      title: '电表功率(kW)',
      dataIndex: 'meterPower',
      key: 'meterPower',
      width: 120,
      align: 'center',
    },
    {
      title: '电表基线(kW)',
      dataIndex: 'meterBaseline',
      key: 'meterBaseline',
      width: 120,
      align: 'center',
    },
    {
      title: '电表调节量(kW)',
      dataIndex: 'meterAdjust',
      key: 'meterAdjust',
      width: 130,
      align: 'center',
    },
  ];

  // 第二步：开停机信息表格列配置
  const operationColumns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
      width: px(150),
    },
    {
      title: '开停机状态',
      dataIndex: 'flag',
      key: 'flag',
      align: 'center',
      width: px(120),
      render: (flag: string) => (
        <span
          style={{
            color: flag === '开机' ? '#52C41A' : '#FF4D4F',
            fontWeight: 'bold',
          }}
        >
          {flag}
        </span>
      ),
    },
  ];

  // 第二步：价格表格列配置
  const priceColumns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
      width: px(150),
    },
    {
      title: '正常电价(元/kWh)',
      dataIndex: 'price',
      key: 'price',
      align: 'center',
      width: px(150),
    },
    {
      title: '中长期电价(元/kWh)',
      dataIndex: 'longPrice',
      key: 'longPrice',
      align: 'center',
      width: px(150),
    },
  ];

  // 第三步：收益计算表格列配置
  const profitColumns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'timeStr',
      key: 'timeStr',
      align: 'center',
      width: px(100),
    },
    {
      title: '整体调节电量(kWh)',
      dataIndex: 'wholeAdjustEnergy',
      key: 'wholeAdjustEnergy',
      align: 'center',
      width: px(150),
    },
    {
      title: '特来电调节电量(kWh)',
      dataIndex: 'teldAdjustEnergy',
      key: 'teldAdjustEnergy',
      align: 'center',
      width: px(160),
    },
    {
      title: '电表调节电量(kWh)',
      dataIndex: 'meterAdjustEnergy',
      key: 'meterAdjustEnergy',
      align: 'center',
      width: px(150),
    },
    {
      title: '中长期电价(元/kWh)',
      dataIndex: 'longPrice',
      key: 'longPrice',
      align: 'center',
      width: px(140),
    },
    {
      title: '实时电价(元/kWh)',
      dataIndex: 'normalRealTimePrice',
      key: 'normalRealTimePrice',
      align: 'center',
      width: px(150),
    },
    {
      title: '整体收益(元)',
      dataIndex: 'wholeProfit',
      key: 'wholeProfit',
      align: 'center',
      width: px(120),
      render: (profit: number) => (
        <span
          style={{
            color: profit > 0 ? '#52C41A' : '#666',
            fontWeight: 'bold',
          }}
        >
          {profit}
        </span>
      ),
    },
    {
      title: '特来电收益(元)',
      dataIndex: 'teldProfit',
      key: 'teldProfit',
      align: 'center',
      width: px(130),
      render: (profit: number) => (
        <span
          style={{
            color: profit > 0 ? '#52C41A' : '#666',
            fontWeight: 'bold',
          }}
        >
          {profit}
        </span>
      ),
    },
    {
      title: '电表收益(元)',
      dataIndex: 'meterProfit',
      key: 'meterProfit',
      align: 'center',
      width: px(120),
      render: (profit: number) => (
        <span
          style={{
            color: profit > 0 ? '#52C41A' : '#666',
            fontWeight: 'bold',
          }}
        >
          {profit}
        </span>
      ),
    },
    {
      title: 'VPP收益(元)',
      dataIndex: 'vppProfit',
      key: 'vppProfit',
      align: 'center',
      width: px(120),
      render: (profit: number) => (
        <span
          style={{
            color: profit > 0 ? '#52C41A' : '#666',
            fontWeight: 'bold',
          }}
        >
          {profit}
        </span>
      ),
    },
  ];

  // 第四步：收益统计表格列配置
  const statisticsColumns: ColumnsType<any> = [
    {
      title: '统计项目',
      dataIndex: 'item',
      key: 'item',
      width: 200,
      align: 'center',
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      width: 150,
      align: 'center',
      render: (value: string, record: any) => {
        const isImportant = ['总收益', '收益率', '总调节量'].includes(
          record.item,
        );
        return (
          <span
            style={{
              color: isImportant ? '#00D4FF' : '#94EFFF',
              fontWeight: isImportant ? 'bold' : 'normal',
              fontSize: isImportant ? '16px' : '14px',
            }}
          >
            {value}
          </span>
        );
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      align: 'center',
    },
  ];

  // 第一步：15分钟调节量数据
  const adjustmentData = apiData?.adjustEvery15MinList || [];

  // 第二步：开停机数据
  const operationData = apiData?.openOrCloseList || [];

  // 第二步：价格数据
  const priceData = apiData?.priceNormalAndLongList || [];

  // 第三步：收益计算数据
  const profitData = apiData?.secondRDailyProfitVos || [];

  // 第四步：收益统计数据
  const statisticsData = apiData?.statisticsVo || [];

  // 根据当前步骤渲染不同内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>每15分钟调节量清单</h3>
            <div
              style={{
                width: '100%',
                minWidth: '500px',
                maxHeight: '400px',
                overflowY: 'scroll',
              }}
            >
              <CustomTable
                dataSource={adjustmentData}
                columns={columns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      case 1:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>开停机信息</h3>
            <div
              style={{
                width: '100%',
                minWidth: '600px',
                maxHeight: '200px',
                overflowY: 'auto',
                marginBottom: '20px',
              }}
            >
              <CustomTable
                dataSource={operationData}
                columns={operationColumns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
            <h3 className={styles.stepTitle}>电价信息</h3>
            <div
              style={{
                width: '100%',
                minWidth: '600px',
                maxHeight: '200px',
                overflowY: 'auto',
              }}
            >
              <CustomTable
                dataSource={priceData}
                columns={priceColumns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>每小时调节量与收益计算</h3>
            <div
              style={{
                width: '100%',
                minWidth: '700px',
                maxHeight: '400px',
                overflowY: 'auto',
              }}
            >
              <CustomTable
                dataSource={profitData}
                columns={profitColumns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      case 3:
        return (
          <div className={styles.stepContent}>
            <h3 className={styles.stepTitle}>今日整体收益统计</h3>
            <div
              style={{
                width: '100%',
                minWidth: '650px',
                maxHeight: '400px',
                overflowY: 'auto',
              }}
            >
              <CustomTable
                dataSource={statisticsData}
                columns={statisticsColumns}
                className={styles.table}
                size="small"
                pagination={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <CustomModal
      open={open}
      title="计算流程详情"
      onCancel={onCancel}
      footer={null}
      className={styles.box}
      width={px(1400)}
      centered={true}
    >
      {/* 步骤条 */}
      <div className={styles.stepsContainer}>
        <Steps
          current={currentStep}
          onChange={setCurrentStep}
          className={styles.customSteps}
          items={steps}
          size="small"
        />
      </div>

      {/* 步骤内容 */}
      {renderStepContent()}
    </CustomModal>
  );
};

export default Index;
