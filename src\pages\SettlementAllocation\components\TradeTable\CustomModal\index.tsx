import CustomModal from '@/components/CustomModal';
import { AGetDailyProgress } from '@/services/settlementAllocation';
import px from '@/utils/px.js';
import { Steps } from 'antd';
import { useEffect, useState } from 'react';
import { IBenefitData } from '../../../typing';
import styles from './index.sass';
import {
  AdjustmentListStep,
  OperationPriceStep,
  ProfitCalculationStep,
  StatisticsStep,
} from './steps';

interface Props {
  open: boolean;
  onCancel: () => void;
  benefitData: IBenefitData;
}

const Index = (props: Props) => {
  const { open, onCancel, benefitData } = props;
  console.log('benifitData', benefitData);
  // 步骤条状态
  const [currentStep, setCurrentStep] = useState(0);
  // 接口数据状态
  const [apiData, setApiData] = useState<any>(null);

  useEffect(() => {
    AGetDailyProgress({ dayStr: benefitData.dayStr }).then((res: any) => {
      setApiData(res);
    });
  }, [benefitData.dayStr]);
  // 步骤配置
  const steps = [
    {
      title: '调节量清单',
      description: '每15分钟调节量及清单详情',
    },
    {
      title: '开停机信息',
      description: '开停机状态与电价信息',
    },
    {
      title: '收益计算',
      description: '每小时调节量与收益计算',
    },
    {
      title: '收益统计',
      description: '今日整体收益统计信息',
    },
  ];

  // 根据当前步骤渲染不同内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <AdjustmentListStep data={apiData?.adjustEvery15MinList || []} />
        );
      case 1:
        return (
          <OperationPriceStep
            operationData={apiData?.openOrCloseList || []}
            priceData={apiData?.priceNormalAndLongList || []}
          />
        );
      case 2:
        return (
          <ProfitCalculationStep data={apiData?.secondRDailyProfitVos || []} />
        );
      case 3:
        return <StatisticsStep data={apiData?.statisticsVo} />;
      default:
        return null;
    }
  };

  return (
    <CustomModal
      open={open}
      title="计算流程详情"
      onCancel={onCancel}
      footer={null}
      className={styles.box}
      width={px(1500)}
      centered={true}
    >
      {/* 步骤条 */}
      <div className={styles.stepsContainer}>
        <Steps
          current={currentStep}
          onChange={setCurrentStep}
          className={styles.customSteps}
          items={steps}
          size="small"
        />
      </div>

      {/* 步骤内容 */}
      {renderStepContent()}
    </CustomModal>
  );
};

export default Index;
