import { openPage } from '@/utils/util';
import { history } from '@umijs/max';
import { useEffect, useState } from 'react';
import { RESPONSE_TYPE } from '../DemandResponse/typing';
import InviteResponse from './components/InviteResponse';
import InviteResult from './components/InviteResult';
import MarketApply from './components/MarketApply';
import MarketResponseV2 from './components/MarketResponseV2';
import MarketResult from './components/MarketResult';
import styles from './index.sass';

// const step = [
//   {
//     name: '编辑需求响应信息',
//     icon: '/step1.png',
//     activeIcon: '/step11.png',
//   },
//   {
//     name: '调度计划信息',
//     icon: '/step2.png',
//     activeIcon: '/step22.png',
//   },
// ];
const Index = () => {
  const searchParams = new URLSearchParams(location.search);
  const responseType = searchParams.get('type');
  const responseName = searchParams.get('name');
  const responseState = searchParams.get('_state');

  const [current, setCurrent] = useState(responseState || '1');
  useEffect(() => {
    if (!current) return;
    const stateIndex = history.location.search.indexOf('_state');
    if (stateIndex > 0) {
      history.push(
        history.location.pathname +
          history.location.search.substring(0, stateIndex) +
          '_state=' +
          current,
      );
    } else {
      history.push(
        history.location.pathname +
          history.location.search +
          '&_state=' +
          current,
      );
    }
  }, [current]);
  return (
    <div className={styles.box}>
      {current === '1' && responseType === RESPONSE_TYPE.market && (
        <MarketResponseV2 onNext={() => setCurrent('2')}></MarketResponseV2>
      )}
      {current === '2' && responseType === RESPONSE_TYPE.market && (
        <MarketResult
          onReturn={() => setCurrent('1')}
          onNext={() => setCurrent('3')}
        ></MarketResult>
      )}
      {current === '3' && responseType === RESPONSE_TYPE.market && (
        <MarketApply
          onNext={() => {
            openPage(
              '/decision-support/scheduling-strategy?type=需求响应&name=' +
                responseName,
            );
          }}
          onReturn={() => setCurrent('2')}
        ></MarketApply>
      )}
      {current === '1' && responseType === RESPONSE_TYPE.invite && (
        <InviteResponse
          onNext={() => {
            setCurrent('2');
          }}
        ></InviteResponse>
      )}
      {current === '2' && responseType === RESPONSE_TYPE.invite && (
        <InviteResult
          onReturn={() => {
            setCurrent('1');
          }}
          onNext={() => {
            openPage(
              '/decision-support/scheduling-strategy?type=需求响应&name=' +
                responseName,
            );
          }}
        ></InviteResult>
      )}
    </div>
  );
};

export default Index;
