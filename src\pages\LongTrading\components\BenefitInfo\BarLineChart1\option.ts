import px from '@/utils/px';
import { chartData } from './mock';

export interface IElectricityData {
  timePoints: string[];
  // 电量数据（柱状图）
  governmentAuthorizedPower: number[];
  yinDongBilateralPower: number[];
  yinDongBiddingPower: number[];
  bilateralTransactionPower: number[];
  biddingTransactionPower: number[];
  listingTransactionPower: number[];
  matchingTransactionPower: number[];

  // 电价数据（折线图）
  dayAheadClearingPrice: number[];
  realTimeClearingPrice: number[];
  realTimeAveragePrice: number[];

  // 额外电量指标（折线图）
  totalMediumLongTermPower: number[];
  dayAheadClearingPower: number[];
  realTimePowerUsage: number[];
  actualPowerUsage80Percent: number[];
}

export const getOption = (data: IElectricityData = chartData) => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      type: 'scroll',
      textStyle: {
        color: 'white',
        fontSize: px(14),
      },
    },

    grid: {
      left: '2%',
      right: '2%',
      bottom: '2%',
      top: '16%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: data.timePoints,
      axisLabel: {
        color: 'white',
        fontSize: px(14),
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '电量 (MWh)',
        position: 'left',
        axisLabel: {
          color: 'white',
          fontSize: px(14),
        },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLine: {
          lineStyle: {
            color: '#5470C6',
          },
        },
      },
      {
        type: 'value',
        name: '收益 (元)',
        position: 'right',
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLabel: {
          color: 'white',
          fontSize: px(14),
        },
        axisLine: {
          lineStyle: {
            color: '#EE6666',
          },
        },
      },
    ],
    series: [
      // 柱状图：电量数据
      {
        name: '竞价电量',
        type: 'bar',
        yAxisIndex: 0,
        barWidth: px(40),
        data: data.governmentAuthorizedPower,
      },
      // 折线图：电价相关
      {
        name: '竞价收益',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        data: data.dayAheadClearingPrice,
      },
    ],
  };
};
