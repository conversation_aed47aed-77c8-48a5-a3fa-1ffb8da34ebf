import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import { IApplyData } from '@/pages/MarketTrading/typing';
import {
  ADeleteResourceData,
  AgetResourcelist,
} from '@/services/marketTrading';
import px from '@/utils/px';
import { Form, Popconfirm } from 'antd';
import styles from './index.sass';
// import { useRequest } from '@umijs/max';
import { IResultList } from '@/pages/MarketTrading/typing';
import { useAntdTable } from 'ahooks';
import { ColumnsType } from 'antd/es/table';

interface Props {
  open: boolean;
  tradeStrategy: IApplyData;
  onSuccess: () => void;
  onCancel: () => void;
}

const Index: React.FC<Props> = (props: Props) => {
  const { open, onCancel } = props;
  const [form] = Form.useForm();
  interface Result {
    list: API.ResourceList[];
    total: number;
  }
  const getTableData = (params: {
    current: number;
    pageSize: number;
  }): Promise<Result> => {
    const { current, pageSize } = params;
    return AgetResourcelist({ current, pageSize })
      .then((res) => {
        if (res.data && res.data.data) {
          const { resourceDeleteList, total } = res.data.data;
          return {
            list: resourceDeleteList,
            total,
          };
        } else {
          // 返回一个空列表或合适的默认值
          return { list: [], total: 0 };
        }
      })
      .catch((error) => {
        console.error('API call failed:', error);
        // 在出错时也返回一个空列表或合适的默认值
        return { list: [], total: 0 };
      });
  };

  const { tableProps, refresh } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 5 }],
    defaultType: 'advance',
  });

  const clickDeleteResult = (rowData: IResultList) => {
    ADeleteResourceData({
      resourceId: rowData.resourceId,
    }).then(() => {
      refresh();
    });
  };
  const columns: ColumnsType<API.ResourceList[]> = [
    {
      title: '资源ID',
      dataIndex: 'resourceId',
      key: 'resourceId',
      align: 'center',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      width: px(200),
      render: (value: null, row: IResultList) => {
        return (
          <div className={styles.operate}>
            <Popconfirm
              title="提示"
              description={<div>确认删除该条交易信息？</div>}
              onConfirm={() => clickDeleteResult(row)}
              okText="确认"
              cancelText="取消"
            >
              <span>删除</span>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  console.log('tableProps', tableProps);

  return (
    <CustomModal
      open={open}
      title="调度资源列表"
      onCancel={onCancel}
      footer={null}
      className={styles.box}
      width={px(1200)}
      centered={true}
    >
      <CustomTable columns={columns} {...tableProps}></CustomTable>
    </CustomModal>
  );
};

export default Index;
