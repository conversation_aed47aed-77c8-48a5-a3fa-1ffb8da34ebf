@import '~@/assets/css/helpers.sass'

.box
  display: flex
  flex-direction: column
  height: fit-content
  padding: px(40)
  align-items: center
.title
  margin-right: px(40)
.process
  display: flex
  flex-direction: row
  font-size: px(18)
  align-items: center
.processItem
  display: flex
  flex-direction: row
  align-items: center
  width: px(180)
.processValue
  background-image: url('@/assets/bg/efficientcontrol/bg6.png')
  background-size: 100% 100%
  width: px(80)
  height: px(80)
  text-align: center
  padding: px(15) px(17)
  font-size: px(16)
  margin: px(30)
.arrow
  background-image: url('@/assets/bg/efficientcontrol/bg7.png')
  background-size: 100% 100%
  height: px(25)
  width: px(40)
.time
  display: flex
  flex-direction: row
  font-size: px(18)
.timeItem
  display: flex
  flex-direction: column
  align-items: center
  width: px(180)
.timeBar
  background-color: #097591
  width: px(200)
  height: px(10)
  border-radius: px(10)
.timeDot
  background-image: url('@/assets/bg/efficientcontrol/bg8.png')
  background-size: 100% 100%
  height: px(22)
  width: px(22)
  transform: translate(px(-20),px(-15))
.timeValue
  font-size: px(16)
  color: #01ccff
  transform: translate(px(-20),px(0))
