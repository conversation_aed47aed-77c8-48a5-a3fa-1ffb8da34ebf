
@import '~@/assets/css/helpers.sass'

.box
  width: 100%
  flex-grow: 1
  display: flex
  flex-direction: column
  margin-top: px(10)
.form
  width: 100%
  display: flex
  flex-direction: row
  align-items: center
  :global
    .ant-form-item .ant-form-item-label >label
      color: white
      font-size: px(18)
      height: px(40)
.title
  font-size: px(20)
  margin-bottom: px(20)
.button
  margin: px(10)
.label
  cursor: default
  color: white
  display: inline-block
  font-size: px(18)
.input,.select
  width: px(250) !important
  margin-right: px(15)
.dateInput
  width: px(300)
  margin-right: px(15)
  outline: none
  border: px(3) solid #20dbfd
  border-radius: px(10)

.table
  flex-grow: 1

.add
  margin-left: auto
  text-align: right

.operate

.longName
  text-overflow: ellipsis
  overflow: hidden
  text-align: center
  white-space: nowrap
  max-width: px(250)
.stepButton
  position: absolute
  right: px(20)
  bottom: px(40)
