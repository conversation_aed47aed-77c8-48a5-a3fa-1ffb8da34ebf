import '@/assets/css/helpers.sass';
import services from '@/services/decisionSupport';
import px from '@/utils/px';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef, useState } from 'react';
import styles from './index.sass';

interface Props {
  selectedDate: dayjs.Dayjs[];
}

const Price: React.FC<Props> = ({ selectedDate }) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>(null);
  const { getDailyPriceV3 } = services.loadForecastController;
  useEffect(() => {
    getDailyPriceV3({
      startDateStr: selectedDate[0].format('YYYY-MM-DD'),
      endDateStr: selectedDate[1].format('YYYY-MM-DD'),
    }).then((res) => {
      setData(res.data);
    });
  }, [selectedDate]);
  useEffect(() => {
    if (!chartRef.current || !data) {
      return;
    }
    const chartInstance = echarts.init(chartRef.current);

    // 构建完整的X轴数据：每天重复timeList
    const fullXAxisData = [];
    if (data.dateList && data.timeList) {
      for (let dateIndex = 0; dateIndex < data.dateList.length; dateIndex++) {
        for (let timeIndex = 0; timeIndex < data.timeList.length; timeIndex++) {
          fullXAxisData.push(data.timeList[timeIndex]);
        }
      }
    }

    // 计算日期分割线位置
    const markLines = [];
    if (data.dateList && data.dateList.length > 1 && data.timeList) {
      const timePointsPerDay = data.timeList.length;
      for (let i = 1; i < data.dateList.length; i++) {
        markLines.push({
          xAxis: i * timePointsPerDay - 0.5, // 在每天的开始位置添加分割线
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
            width: 2,
            type: 'dashed',
          },
          label: {
            show: true,
            position: 'insideEndBottom',
            formatter: data.dateList[i],
            color: '#fff',
            fontSize: px(16),
            rotate: 0, // 横向显示
            offset: [px(100), 0], // 向右偏移
          },
        });
      }
    }

    // 添加第一个日期的标签（在起始位置）
    markLines.push({
      xAxis: 0, // 在X轴起始位置
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.3)',
        width: 2,
        type: 'dashed',
      },
      label: {
        show: true,
        position: 'insideEndBottom',
        formatter: data.dateList[0],
        color: '#fff',
        fontSize: px(16),
        rotate: 0, // 横向显示
        offset: [px(100), 0], // 向右偏移
      },
    });

    // 配置项和数据
    const options = {
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          if (!params || params.length === 0) return '';

          const dataIndex = params[0].dataIndex;
          const timePointsPerDay = data.timeList ? data.timeList.length : 24;
          const dayIndex = Math.floor(dataIndex / timePointsPerDay);
          const timeIndex = dataIndex % timePointsPerDay;

          const currentDate =
            data.dateList && data.dateList[dayIndex]
              ? data.dateList[dayIndex]
              : '';
          const currentTime =
            data.timeList && data.timeList[timeIndex]
              ? data.timeList[timeIndex]
              : '';

          let result = `<div style="margin-bottom: 5px;"><strong>${currentDate} ${currentTime}</strong></div>`;

          params.forEach((param: any) => {
            const displayValue =
              param.value === null || param.value === undefined
                ? '-'
                : param.value;
            result += `<div style="margin: 2px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                param.color
              };"></span>
              ${param.seriesName}: ${displayValue} ${
              displayValue !== '-' ? data.unit || '' : ''
            }
            </div>`;
          });

          return result;
        },
      },
      legend: {
        data: ['现货价格'],
        textStyle: { color: '#fff', fontSize: px(16) },
        left: 'center',
        right: 'center',
      },
      grid: {
        left: px(10),
        right: px(22),
        top: px(55),
        bottom: px(0),
        containLabel: true,
      },
      dataZoom: [
        {
          type: 'inside', // 或 'inside'
          start: 0, // 开始位置（0-100）
          end: 100,
        },
      ],

      xAxis: {
        boundaryGap: false,
        type: 'category',
        data: fullXAxisData.length > 0 ? fullXAxisData : data.timeList,
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
          showMaxLabel: true, // 显示最后一个标签
        },
      },
      yAxis: {
        type: 'value',
        name: data.unit,
        nameTextStyle: { color: '#fff', fontSize: px(16) },
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
          },
        },
      },
      series: [
        {
          name: '现货价格',
          type: 'line',
          smooth: true,
          data: data.valueList,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#006ebf',
          },
          lineStyle: {
            color: '#006ebf',
            width: px(3),
          },
          markLine: {
            silent: true,
            data: markLines,
          },
        },
      ],
    };
    chartInstance.setOption(options);
  }, [data]);

  return <div ref={chartRef} className={styles.price} />;
};

export default Price;
