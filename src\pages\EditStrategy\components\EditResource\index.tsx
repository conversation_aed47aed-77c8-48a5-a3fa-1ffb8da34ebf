import CustomButton from '@/components/CustomButton';
import CustomStepButton from '@/components/CustomStepButton';

import CustomInput from '@/components/CustomInput';
import { longRender } from '@/components/CustomLongRender';
import CustomSelect from '@/components/CustomSelect';
import CustomTable from '@/components/CustomTable';
import CustomTableButton from '@/components/CustomTableButton';
import {
  ADeleteResource,
  AGenerateStrategy,
  AGetResourceTable,
  ARecoverResource,
} from '@/services/editStrategy';
import { AGetResourceType } from '@/services/efficientControl';
import { useRequest } from '@umijs/max';
import { useAntdTable } from 'ahooks';
import { Form, Popconfirm, Spin, message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useRef, useState } from 'react';
import { IResourceData } from '../../typing';
import styles from './index.sass';

interface Props {
  name?: string;
  onClickNext: () => void;
}
const Index: React.FC<Props> = (props: Props) => {
  const { onClickNext } = props;
  const container = useRef<HTMLDivElement>(null);
  const [allResourceType, setAllResourceType] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const { run } = useRequest(AGenerateStrategy, {
    manual: true,
  });
  const [form] = Form.useForm();
  const getTableData = (
    params: { pageSize: number; current: number },
    formData?: {
      resourceName?: string;
      resourceType?: string;
      isDeleted: string;
    },
  ) => {
    console.log(params, formData);
    const { pageSize, current } = params;
    const { resourceType, resourceName, isDeleted = 'false' } = formData || {};
    return AGetResourceTable({
      pageSize,
      current,
      resourceType,
      resourceName,
      isDeleted,
    });
  };

  const { tableProps, search, refresh } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
    defaultType: 'advance',
  });

  const { submit, reset } = search;

  useEffect(() => {
    AGetResourceType().then((res) => {
      setAllResourceType(res);
    });
  }, []);
  const onDeleteResource = (row: IResourceData) => {
    ADeleteResource({
      resourceId: row.resourceId,
    }).then(() => {
      message.success('删除成功！');
      refresh();
    });
  };
  const onRecoverResource = (row: IResourceData) => {
    ARecoverResource({
      resourceId: row.resourceId,
    }).then(() => {
      message.success('恢复成功！');
      refresh();
    });
  };
  const handleNext = () => {
    setLoading(true);
    run()
      .then(() => {
        onClickNext?.();
        setLoading(false);
      })
      .catch((e) => {
        console.log(e);
        message.error('生成失败，请点击重试！');
        setLoading(false);
      });
  };
  const columns: ColumnsType<IResourceData> = [
    {
      title: '资源点ID',
      dataIndex: 'resourceId',
      key: 'resourceId',
      align: 'center',
    },
    {
      title: '资源点名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: longRender,
    },
    {
      title: '资源类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (value: any) => {
        return <div className={styles.longName}>{value}</div>;
      },
    },
    {
      title: '所属用户',
      dataIndex: 'resourceUserName',
      key: 'resourceUserName',
    },
    {
      title: '联系人',
      dataIndex: 'personName',
      key: 'personName',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (value: null, row: IResourceData) => {
        return (
          <div className={styles.operate}>
            {row.deleted ? (
              <Popconfirm
                title="提示"
                description="确认恢复该资源点？（恢复后该资源点将参与本次日前交易）"
                onConfirm={() => onRecoverResource(row)}
                okText="确认"
                cancelText="取消"
              >
                <div>
                  <CustomTableButton name="恢复"></CustomTableButton>
                </div>
              </Popconfirm>
            ) : (
              <Popconfirm
                title="提示"
                description="确认删除该资源点？（删除后该资源点将不再参与本次日前交易）"
                onConfirm={() => onDeleteResource(row)}
                okText="确认"
                cancelText="取消"
              >
                <div>
                  <CustomTableButton name="删除"></CustomTableButton>
                </div>
              </Popconfirm>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Spin spinning={loading} tip={'正在生成交易策略···'} fullscreen></Spin>
      <div className={styles.box} ref={container}>
        <Form className={styles.form} form={form}>
          <Form.Item name="resourceName" label="资源点名称">
            <CustomInput
              className={styles.input}
              placeholder="请输入资源点名称"
            />
          </Form.Item>
          <Form.Item name="resourceType" label="资源类型">
            <CustomSelect
              className={styles.select}
              allowClear
              options={allResourceType.map((item) => {
                return {
                  label: item,
                  value: item,
                };
              })}
            ></CustomSelect>
          </Form.Item>
          <Form.Item name="isDeleted" label="是否参与本次交易">
            <CustomSelect
              defaultValue={'false'}
              className={styles.select}
              options={[
                {
                  label: '是',
                  value: 'false',
                },
                {
                  label: '否',
                  value: 'true',
                },
              ]}
            ></CustomSelect>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={submit}>
              筛选
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton onClick={reset}>重置</CustomButton>
          </Form.Item>
        </Form>
        <div className={styles.table}>
          <CustomTable
            size="small"
            {...tableProps}
            columns={columns}
          ></CustomTable>
        </div>
        <CustomStepButton
          name="下一步"
          className={styles.stepButton}
          onClick={handleNext}
        ></CustomStepButton>
      </div>
    </>
  );
};

export default Index;
