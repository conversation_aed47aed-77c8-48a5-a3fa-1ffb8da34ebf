@import '@/assets/css/helpers.sass'

.box
  margin-top: px(10)
  width: 100%
  flex-grow: 1
  display: flex
  flex-direction: column
  color: white

.title
  font-size: px(20)
  margin-bottom: px(10)
  .text
    margin-right: px(10)
    font-family: youshe
    font-size: px(25)
.form
  display: flex
  flex-direction: row
  align-items: center
  :global
    .ant-form-item .ant-form-item-label >label
      color: white
      font-size: px(18)
      height: px(40)
    .ant-form-item
      margin-bottom: px(10)
.button
  margin: 0 px(10)
.input,.select
  width: px(150)!important
  margin-right: px(15)

.dateInput
  width: px(380)
  margin-right: px(15)
  outline: none
  border: px(3) solid #20dbfd
  border-radius: px(10)
.table
  flex-grow: 1
  width: px(1380)
  :global
    .ant-table-content
      &::-webkit-scrollbar
        width: px(10)
        height: px(10)
        background-color: rgba(6, 48, 109, 1)
      &::-webkit-scrollbar-thumb
        background-color: #05b4c7
        border-radius: px(5)
.add
  margin-bottom: px(10)

.operate
  color: yellow
  & span
    margin: px(10)
    cursor: pointer
  & span:hover
    color: white
