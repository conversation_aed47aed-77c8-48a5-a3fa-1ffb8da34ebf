import CustomTable from '@/components/CustomTable';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import styles from '../index.sass';

interface StatisticsStepProps {
  data: any;
}

const StatisticsStep: React.FC<StatisticsStepProps> = ({ data }) => {
  // 第四步：收益统计表格列配置
  const columns: ColumnsType<any> = [
    {
      title: '统计项目',
      dataIndex: 'item',
      key: 'item',
      width: 200,
      align: 'center',
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      width: 200,
      align: 'center',
      render: (value: any, record: any) => {
        const isProfit = record.item.includes('收益');
        const isEnergy = record.item.includes('调节电量');
        const isPrice = record.item.includes('电价');

        let displayValue = value;
        if (typeof value === 'number') {
          if (isProfit || isPrice) {
            displayValue = value.toFixed(2);
          } else if (isEnergy) {
            displayValue = value.toFixed(2);
          }
        }

        return (
          <span
            style={{
              color: isProfit ? '#52C41A' : '#00D4FF',
              fontWeight: isProfit ? 'bold' : 'normal',
              fontSize: isProfit ? '16px' : '14px',
            }}
          >
            {displayValue} {record.unit}
          </span>
        );
      },
    },
  ];

  // 转换数据格式
  const statisticsData = data
    ? [
        {
          key: '1',
          item: '日期',
          value: data.dayStr,
          unit: '',
        },
        {
          key: '2',
          item: '整体调节量',
          value: data.wholeAdjustEnergy,
          unit: 'MW',
        },
        {
          key: '3',
          item: '特来电调节量',
          value: data.teldAdjustEnergy,
          unit: 'MW',
        },
        {
          key: '4',
          item: '电表调节量',
          value: data.meterAdjustEnergy,
          unit: 'MW',
        },
        {
          key: '5',
          item: '调节电价',
          value: data.adjustPrice,
          unit: '元/MWh',
        },
        {
          key: '6',
          item: '整体收益',
          value: data.wholeProfit,
          unit: '元',
        },
        {
          key: '7',
          item: '特来电收益',
          value: data.teldProfit,
          unit: '元',
        },
        {
          key: '8',
          item: '电表收益',
          value: data.meterProfit,
          unit: '元',
        },
        {
          key: '9',
          item: 'VPP收益',
          value: data.vppProfit,
          unit: '元',
        },
      ]
    : [];

  return (
    <div className={styles.stepContent}>
      <h3 className={styles.stepTitle}>收益统计</h3>
      <div
        style={{
          width: '100%',
          minWidth: '400px',
          maxHeight: '400px',
          overflowY: 'scroll',
        }}
      >
        <CustomTable
          dataSource={statisticsData}
          columns={columns}
          className={styles.table}
          size="small"
          pagination={false}
        />
      </div>
    </div>
  );
};

export default StatisticsStep;
