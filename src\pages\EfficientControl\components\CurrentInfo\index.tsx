import {
  AGetCompleteRate,
  AGetUseagePercent,
} from '@/services/efficientControl';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';
import styles from './index.sass';
import { getOption } from './option';

interface Props {
  name?: string;
}

const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  const completeBox = useRef<HTMLDivElement>(null);
  const proportionBox = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let completeChart = echarts.init(completeBox.current as HTMLDivElement);

    AGetCompleteRate().then((res) => {
      completeChart.setOption(getOption(res * 100, 100, 'complete'));
    });
    let proportionChart = echarts.init(proportionBox.current as HTMLDivElement);
    AGetUseagePercent().then((res) => {
      proportionChart.setOption(getOption(res * 100, 100, 'proportion'));
    });
  }, []);
  return (
    <div className={styles.box}>
      <div className={styles.completeTitle}>最新调度计划完成率</div>
      <div ref={completeBox} className={styles.completeBox}></div>
      <div className={styles.proportionTitle}>最新调度计划资源使用占比</div>
      <div ref={proportionBox} className={styles.proportionBox}></div>
    </div>
  );
};

export default Index;
