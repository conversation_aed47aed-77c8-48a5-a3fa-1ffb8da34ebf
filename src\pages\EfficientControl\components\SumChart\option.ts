import px from '@/utils/px';

export const getOption = (axis: string[], value: number[]) => {
  console.log(value);
  return {
    tooltip: {
      trigger: 'item',
      show: true,
      textStyle: {
        fontSize: px(14),
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '3%',
      bottom: px(40),
      containLabel: true,
    },
    xAxis: [
      {
        type: 'value',
        axisLabel: { color: 'white', fontSize: px(14) },
      },
    ],
    yAxis: [
      {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLabel: { color: 'white', fontSize: px(14) },
        data: axis,
      },
    ],
    dataZoom: [
      {
        id: 'dataZoomY',
        type: 'slider',
        yAxisIndex: [0],
        filterMode: 'empty',
        start: 0,
        end:
          10 / axis.length >= 1 ? 100 : ((10 / axis.length) * 100).toFixed(0),
        orient: 'horizontal',
        bottom: px(20),
        height: px(15),
      },
    ],
    series: [
      {
        type: 'bar',
        label: {
          show: true,
          position: 'inside',
          fontSize: px(14),
        },
        showBackground: true,
        itemStyle: {
          borderRadius: [0, px(15), px(15), 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(6, 48, 109,1)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#188df0', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        emphasis: {
          focus: 'series',
          fontSize: px(14),
        },
        data: value,
      },
    ],
  };
};
