import Tab from '@/components/Tab';
import { ITabOptions } from '@/components/Tab/typing';
import { AGetSourceUseCount } from '@/services/efficientControl';
import { datetimeFormat } from '@/utils/util';
import { useModel, useRequest } from '@umijs/max';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';
import styles from './index.sass';
import { getOption } from './option';

interface Props {
  name?: string;
}

const Index: React.FC<Props> = (props: Props) => {
  const defalutTime = [dayjs().add(-7, 'd'), dayjs().add(0, 'd')];
  const {} = props;

  const container = useRef<HTMLDivElement>(null);
  const { run } = useRequest(AGetSourceUseCount, {
    manual: true,
  });
  const myChart = useRef<any>();
  const { unitType } = useModel('unitType');
  useEffect(() => {
    if (container.current) {
      myChart.current = echarts.init(container.current as HTMLDivElement);
      run({
        startTime: defalutTime[0].format(datetimeFormat),
        endTime: defalutTime[1].format(datetimeFormat),
      }).then((res) => {
        if (res) {
          myChart.current.setOption(
            getOption(res.resourceNameList, res.countList),
          );
        }
      });
    }
  }, [unitType]);
  const onSearch = (value: ITabOptions) => {
    run({
      startTime: dayjs(value.time[0]).format(datetimeFormat),
      endTime: dayjs(value.time[1]).format(datetimeFormat),
    }).then((res) => {
      if (res) {
        myChart.current.setOption(
          getOption(res.resourceNameList, res.countList),
        );
      }
    });
  };
  return (
    <>
      <Tab
        title={`${unitType}各资源点累计调度次数`}
        optionTypes={['date']}
        onSearch={onSearch}
        defaultTimeRange={defalutTime}
      ></Tab>
      <div className={styles.box} ref={container}></div>
    </>
  );
};

export default Index;
