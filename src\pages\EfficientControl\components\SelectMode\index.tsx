import CustomTipModal from '@/components/CustomTipModal';
import { AChangeScheduleMode } from '@/services/efficientControl';
import { useModel } from '@umijs/max';
import React, { useState } from 'react';
import styles from './index.sass';
interface Props {
  name?: string;
}
type Mode = {
  isMarket: boolean;
};
const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  const { scheduleMode, setScheduleMode } = useModel('globalData');
  const [open, setOpen] = useState(false);
  const selectMode = (selected: Mode) => {
    if (selected !== scheduleMode) {
      setOpen(true);
    }
  };
  const handleOk = () => {
    AChangeScheduleMode({ isMarket: !scheduleMode.isMarket }).then(() => {
      setScheduleMode({ isMarket: !scheduleMode.isMarket }); // 更改全局变量
      setOpen(false);
    });
  };
  const handleCancel = () => {
    setOpen(false);
  };
  return (
    <div className={styles.box}>
      <div className={styles.title}>调度模式选择</div>
      <div className={styles.items}>
        <div
          className={`${styles.market} ${
            scheduleMode.isMarket && styles.selected
          }`}
          onClick={() => selectMode({ isMarket: true })}
        >
          市场调度模式
        </div>
        <div
          className={`${styles.cost} ${
            !scheduleMode.isMarket && styles.selected
          }`}
          onClick={() => selectMode({ isMarket: false })}
        >
          成本调度模式
        </div>
      </div>
      <CustomTipModal
        title="提示"
        open={open}
        onOk={handleOk}
        onCancel={handleCancel}
        centered
      >
        确认切换为
        {scheduleMode.isMarket
          ? '成本调度模式？切换后将在明日生效，成本调度模式下，平台将根据内部所有资源点实际发用电情况进行调度，无法参与市场交易。'
          : '市场调度模式？切换后将在明日生效，市场调度模式下，平台会根据您参与市场交易情况对资源点进行调度。'}
      </CustomTipModal>
    </div>
  );
};

export default Index;
