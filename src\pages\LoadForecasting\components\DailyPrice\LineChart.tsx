import '@/assets/css/helpers.sass';
import services from '@/services/decisionSupport';
import px from '@/utils/px';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef, useState } from 'react';
import styles from './index.sass';

interface Props {
  selectedDate: dayjs.Dayjs;
}

const Price: React.FC<Props> = ({ selectedDate }) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>(null);
  const { setMaxPrice, setMinPrice } = useModel('forecast');
  const { getDailyPrice } = services.loadForecastController;

  useEffect(() => {
    const dateStr = selectedDate.format('YYYY-MM-DD');
    getDailyPrice({ dateStr }).then((res) => {
      setData(res.data);
      setMaxPrice(res.data.maxDiff);
      setMinPrice(res.data.minDiff);
    });
  }, [selectedDate]);

  useEffect(() => {
    if (!chartRef.current || !data) {
      return;
    }
    const chartInstance = echarts.init(chartRef.current);

    // 配置项和数据
    const options = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['D日现货价格', 'D-1日现货价格'],
        textStyle: { color: '#fff', fontSize: px(16) },
        left: 'center',
        right: 'center',
      },
      grid: {
        left: px(10),
        right: px(45),
        top: px(50),
        bottom: px(0),
        containLabel: true,
      },
      dataZoom: [
        {
          type: 'inside', // 或 'inside'
          start: 0, // 开始位置（0-100）
          end: 100,
        },
      ],

      xAxis: {
        boundaryGap: false,
        type: 'category',
        data: data.timeList,
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
          interval: 5,
          showMaxLabel: true, // 显示最后一个标签
        },
      },
      yAxis: {
        type: 'value',
        /*         name: data.unit,
        nameTextStyle: { color: '#fff', fontSize: px(16) }, */
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
          },
        },
      },
      series: [
        {
          name: 'D日现货价格',
          type: 'line',
          smooth: true,
          data: data.dayAheadList1,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#ff1515',
          },
          lineStyle: {
            color: '#ff1515',
            width: px(3),
          },
        },
        {
          name: 'D-1日现货价格',
          type: 'line',
          smooth: true,
          data: data.dayAheadList0,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#006ebf',
          },
          lineStyle: {
            color: '#006ebf',
            width: px(3),
          },
        },
      ],
    };
    chartInstance.setOption(options);
  }, [data]);

  return <div ref={chartRef} className={styles.price} />;
};

export default Price;
