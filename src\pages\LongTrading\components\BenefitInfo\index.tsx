import CustomDateRange from '@/components/CustomDateRangeV2';
import CustomSelectV2 from '@/components/CustomSelectV2';
import Tab from '@/components/Tab';
import { getRevenueUsingGET } from '@/services/marketSituation2FController';
import { DatePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import BarChart from './BarChart';
import BarLine<PERSON>hart from './BarLineChart';
// import BarLineChart1 from './BarLineChart1';
import px from '@/utils/px';
import styles from './index.sass';

export default function () {
  const [timeRangeType, setTimeRangeType] = useState<'date' | 'month'>('date');
  const [loading, setLoading] = useState<boolean>(false);
  const [revenueData, setRevenueData] = useState<any>(null);
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs('2025-06-01'),
    dayjs('2025-06-30'),
  ]);
  const [monthRange, setMonthRange] = useState<[Dayjs, Dayjs]>([
    dayjs('2025-01-01'),
    dayjs('2025-05-01'),
  ]);
  // Handle date range change for daily selection
  const handleDateRangeChange = (dates: [Dayjs, Dayjs]) => {
    setDateRange(dates);
  };

  // Handle month range change for monthly selection
  const handleMonthRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      // Set day to 1 for both start and end dates to ensure we're only working with months
      const startMonth = dates[0].startOf('month');
      const endMonth = dates[1].startOf('month');
      setMonthRange([startMonth, endMonth]);
    }
  };

  // Fetch revenue data when time range changes
  useEffect(() => {
    const fetchRevenueData = async () => {
      setLoading(true);
      try {
        let startTime, endTime;

        if (timeRangeType === 'date') {
          startTime = dateRange[0].format('YYYY-MM-DD');
          endTime = dateRange[1].format('YYYY-MM-DD');
        } else {
          startTime = monthRange[0].format('YYYY-MM');
          endTime = monthRange[1].format('YYYY-MM');
        }

        const response = await getRevenueUsingGET({
          timeType: timeRangeType,
          startTime,
          endTime,
        });

        if (response.success && response.data) {
          console.log('Revenue data:', response.data);
          setRevenueData(response.data);
        } else {
          console.error('Failed to fetch revenue data');
        }
      } catch (error) {
        console.error('Error fetching revenue data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRevenueData();
  }, [timeRangeType, dateRange, monthRange]);

  return (
    <div className={styles.box}>
      <div className={styles.time}>
        时间范围选择：
        <CustomSelectV2
          style={{ width: px(120), marginRight: px(10) }}
          defaultValue="date"
          options={[
            { label: '日度', value: 'date' },
            { label: '月度', value: 'month' },
          ]}
          onChange={(value) => setTimeRangeType(value as 'date' | 'month')}
        />
        {timeRangeType === 'date' ? (
          <CustomDateRange
            defaultTimeRange={[dayjs('2025-06-01'), dayjs('2025-06-30')]}
            onChange={handleDateRangeChange}
          />
        ) : (
          <DatePicker.RangePicker
            allowClear={false}
            picker="month"
            format="YYYY-MM"
            defaultValue={[dayjs('2025-01'), dayjs('2025-05')]}
            onChange={handleMonthRangeChange}
            style={{
              width: px(230),
              backgroundColor: 'transparent',
              color: 'white',
              borderColor: 'rgba(255, 255, 255)',
            }}
            className={styles.datePicker}
          />
        )}
      </div>
      <div className={styles.content}>
        <div className={styles.pane}>
          <Tab title={'外部交易收益统计'}></Tab>
          <BarLineChart type="extra" data={revenueData} loading={loading} />
        </div>
        <div className={styles.pane}>
          <Tab title={'内部交易收益统计'}></Tab>
          <BarLineChart type="inner" data={revenueData} loading={loading} />
        </div>
        <div className={styles.pane2}>
          <Tab title={'收益合计'}></Tab>
          <BarChart data={revenueData} />
        </div>
        {/* <div className={styles.pane}>
          <Tab title={'为稳定市场竞价交易'}></Tab>
          <BarLineChart1></BarLineChart1>
        </div> */}
      </div>
    </div>
  );
}
