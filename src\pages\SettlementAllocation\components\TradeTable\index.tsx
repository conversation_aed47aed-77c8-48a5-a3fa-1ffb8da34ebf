import CustomTable from '@/components/CustomTable';
import { AGetDailyBenefitList } from '@/services/settlementAllocation';
import px from '@/utils/px';
import { useModel } from '@umijs/max';
import { useAntdTable } from 'ahooks';
import { Form } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useRef, useState } from 'react';
import { IBenefitData } from '../../typing';
import CustomModal from './CustomModal';
import styles from './index.sass';

interface Props {
  name?: string;
}
const Index: React.FC<Props> = (props: Props) => {
  const {} = props;

  const { unitType } = useModel('unitType');
  const container = useRef<HTMLDivElement>(null);
  const [open, setOpen] = useState(false);
  const [currentBenefit, setCurrentBenefit] = useState<IBenefitData>();
  const [form] = Form.useForm();
  const getTableData = (params: { pageSize: number; current: number }) => {
    const { pageSize, current } = params;
    return AGetDailyBenefitList({
      pageSize,
      current,
    });
  };

  const { tableProps } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
    defaultType: 'advance',
    refreshDeps: [unitType],
  });

  const clickInner = (data: IBenefitData) => {
    setOpen(true);
    setCurrentBenefit(data);
  };
  const clickCancel = () => {
    setOpen(false);
  };

  /*   const clickDeleteBenefit = (data: IBenefitData) => {
    ADeleteTradeData({ profitId: data.profitId }).then(() => {
      message.success('删除成功！');
      refresh();
    });
  }; */
  const columns: ColumnsType<IBenefitData> = [
    {
      title: '时间',
      dataIndex: 'dayStr',
      key: 'dayStr',
      align: 'center',
      fixed: 'left',
      width: px(150),
    },
    {
      title: '整体调节量(MW)',
      dataIndex: 'wholeAdjustEnergy',
      key: 'wholeAdjustEnergy',
      align: 'center',
    },
    {
      title: '特来电调节量(MW)',
      dataIndex: 'teldAdjustEnergy',
      key: 'teldAdjustEnergy',
      align: 'center',
    },
    {
      title: '电表调节量(MW)',
      dataIndex: 'meterAdjustEnergy',
      key: 'meterAdjustEnergy',
      align: 'center',
    },
    {
      title: '调节电价(元/MWh)',
      dataIndex: 'adjustPrice',
      key: 'adjustPrice',
      align: 'center',
    },
    {
      title: '整体收益(元)',
      dataIndex: 'wholeProfit',
      key: 'wholeProfit',
      align: 'center',
    },
    {
      title: '特来电收益(元)',
      dataIndex: 'teldProfit',
      key: 'teldProfit',
      align: 'center',
    },
    {
      title: '电表收益(元)',
      dataIndex: 'meterProfit',
      key: 'meterProfit',
      align: 'center',
    },
    {
      title: '虚拟电厂收益(元)',
      dataIndex: 'vppProfit',
      key: 'vppProfit',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: px(180),
      render: (value: null, row: IBenefitData) => {
        return (
          <div className={styles.operate}>
            {/* <span onClick={() => clickEditBenefit(row)}>编辑</span> */}
            {/*             <Popconfirm
              title="提示"
              description="确认删除该条数据？（删除后不可恢复）"
              onConfirm={() => clickDeleteBenefit(row)}
              okText="确认"
              cancelText="取消"
            >
              <span>删除</span>
            </Popconfirm> */}
            {/* <span>交易详情</span> */}
            <span onClick={() => clickInner(row)}>计算流程</span>
            {/*             <span onClick={() => clickInner(row)}>收益详情</span> */}
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className={styles.box} ref={container}>
        {/* <div className={styles.title}>收益结算信息</div> */}
        {/*         <Form className={styles.form} form={form}>
          <Form.Item name="tradeType" label="交易类型">
            <CustomSelect
              defaultValue={TRADE_TYPE.daytrade}
              className={styles.select}
              options={tradeTypeOptions}
            ></CustomSelect>
          </Form.Item>
          <Form.Item name="name" label="交易名称">
            <CustomInput
              className={styles.input}
              placeholder="请输入交易名称"
            />
          </Form.Item>
          <Form.Item name="timeRange" label="时间范围">
            <CustomDateRange
              presets={rangePresets}
              className={styles.dateInput}
              format={dateFormat}
            />
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={submit}>
              筛选
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={reset}>
              重置
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton
              onClick={() => {
                ADownLoadExcel({ type: '交易收益' }).then(() => {
                  message.success('下载成功！');
                });
              }}
            >
              下载
            </CustomButton>
          </Form.Item>
          <Form.Item className={styles.add}>
            <CustomButton onClick={clickAddBenefit}>
              添加收益结算信息
            </CustomButton>
          </Form.Item>
        </Form> */}

        <div className={styles.table}>
          <CustomTable
            {...tableProps}
            scroll={{ x: px(1800) }}
            columns={columns}
          ></CustomTable>
        </div>
      </div>
      {currentBenefit && (
        <CustomModal
          benefitData={currentBenefit}
          open={open}
          onCancel={clickCancel}
        ></CustomModal>
      )}
      {/* <BenefitInfo
        open={openBenefit}
        onCancel={cancelBenefitModal}
        onSuccess={ensureBenefitModal}
        benefitData={currentBenefit}
      ></BenefitInfo> */}
    </>
  );
};

export default Index;
