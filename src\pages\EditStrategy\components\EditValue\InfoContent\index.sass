@import '~@/assets/css/helpers.sass'
.infoContent
    display: flex
    flex-direction: row
    align-items: center
    justify-content: center
    gap: px(15)

    .devider
        background-image: url('@/assets/bg/EditStategy/devider.png')
        background-repeat: no-repeat
        width: px(3)
        height: px(33)
    .right
        display: flex
        flex-direction: row
        align-items: center
    
        .statusIcon
            background-image: url('@/assets/bg/EditStategy/Editing.svg')
            background-repeat: no-repeat
            background-size: 100% 100% 
            margin-right: px(10)
            margin-top: px(4)
            width: px(20)
            height: px(20)
       
        .infoName
            font-weight: bold
            font-size: px(20)
            color: #20DBFD