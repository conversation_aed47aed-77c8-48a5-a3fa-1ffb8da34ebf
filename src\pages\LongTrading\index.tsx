import CustomTab from '@/components/CustomTab';
import { TabsProps } from 'antd';
import BenefitInfo from './components/BenefitInfo';
import LongTermChart from './components/LongTermChart';

import styles from './index.sass';

const items: TabsProps['items'] = [
  {
    key: '1',
    label: '2F机组电量与电价趋势图',
    children: <LongTermChart></LongTermChart>,
  },
  {
    key: '3',
    label: '2F机组售电公司内外部中长期交易收益情况',
    children: <BenefitInfo></BenefitInfo>,
  },
];

export default function () {
  return (
    <CustomTab className={styles.tab} defaultActiveKey="1" items={items} />
  );
}
