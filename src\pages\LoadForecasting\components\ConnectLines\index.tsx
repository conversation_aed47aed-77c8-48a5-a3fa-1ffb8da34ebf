import CustomModal from '@/components/CustomModal';
import Tab from '@/components/Tab';
import px from '@/utils/px';
import { ExpandOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import styles from './index.sass';
import LineChart from './LineChart';
import MulitiDateChart from './MulitiDateChart';
interface Props {
  selectedDate: dayjs.Dayjs;
}

const ConnectLines: React.FC<Props> = ({ selectedDate }) => {
  const { maxLine, minLine } = useModel('forecast');
  const [modalOpen, setModalOpen] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(
    null,
  );

  const handleExpandClick = () => {
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
  };

  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  return (
    <>
      <div className={styles.header}>
        <Tab title="联络线受电负荷预测(MW)"></Tab>
        <button
          type="button"
          className={styles.expandButton}
          onClick={handleExpandClick}
        >
          <ExpandOutlined /> 放大
        </button>
      </div>
      <div className={styles.value}>
        <div className={styles.max}>最小差值:{minLine}</div>
        <div className={styles.max}>最大差值:{maxLine}</div>
      </div>
      <LineChart selectedDate={selectedDate} />

      <CustomModal
        open={modalOpen}
        title="联络线受电负荷预测(MW) - 详细视图"
        onCancel={handleModalClose}
        footer={null}
        width={px(1500)}
        centered={true}
      >
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <div className={styles.dateRangeContainer}>
              <span className={styles.dateLabel}>时间范围：</span>
              <div className={styles.dateRange}>
                <DatePicker.RangePicker
                  allowClear={false}
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  format="YYYY-MM-DD"
                  placeholder={['开始日期', '结束日期']}
                />
              </div>
            </div>
          </div>
          <div className={styles.modalChart}>
            {dateRange ? (
              <MulitiDateChart selectedDate={dateRange} />
            ) : (
              <LineChart selectedDate={selectedDate} />
            )}
          </div>
        </div>
      </CustomModal>
    </>
  );
};

export default ConnectLines;
