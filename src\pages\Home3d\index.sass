@import '~@/assets/css/helpers.sass'

@font-face
    font-family: '庞门正道标题体'
    src: url(@/assets/font/YouSheBiaoTiHei-2.ttf)
.hidden
    visibility: hidden
    opacity: 0
    transition: visibility 0s, opacity 0.5s linear

@keyframes slideInUp
    from
        transform: translateY(100%)
        opacity: 0

    to
        transform: translateY(0)
        opacity: 1

@keyframes slideInRight
    from
        transform: translateX(-30%)
        opacity: 0

    to
        transform: translateX(0)
        opacity: 1

.animateIn
    visibility: visible
    opacity: 1
    animation: slideInUp 0.5s ease-out forwards

.animateInRight
    visibility: visible
    opacity: 1
    animation: slideInRight 0.5s ease-out forwards

.container
    height: 100%
    width: 100%
    position: relative
    .title
        position: absolute
        width: px(247)
        height: px(60)
        z-index: 10
        color: #EBF0F4
        // background: linear-gradient(180.00deg, rgb(255, 255, 255),rgb(159, 201, 241))
        // -webkit-background-clip: text
        // -webkit-text-fill-color: transparent
        // background-clip: text
        // text-fill-color: transparent
        font-size: px(26)
        font-weight: px(400)
        line-height: px(60)
        font-family: 庞门正道标题体
        text-align: center
        background-image: url(../../assets/bg/textBg.png)
        background-size: 100% 100%
    .content
        position: absolute
        z-index: 11
        // border: 1px solid red
        cursor: pointer

    .rightDes
        position: absolute
        z-index: 10
        cursor: pointer
        .desTitle
            background-image: url(../../assets/bg/titleBg.png)
            background-size: 40% 100%
            background-repeat: no-repeat
            line-height: px(28)
            padding-left: px(9)
            font-size: px(16)
            color: white

        .desContent
            background-image: url(../../assets/bg/contentBg.png)
            background-size: 100% 100%
            min-height: px(142)
            font-size: px(14)
            color: white
            padding: px(15)
            padding-top: px(20)
.loading
    position: absolute
    top: 50%
    left: 50%
    transform: translate(-50%, -50%)
    z-index: 100
    .loadingText
        width: px(300)
        transform: translate(-50%, 0%)
        font-size: px(20)
        color: white
