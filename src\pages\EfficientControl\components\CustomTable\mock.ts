export const data = [
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
  {
    scheduleId: 0,
    resourceName: '充电桩A',
    decisionId: '计划发电20kwh',
    startTime: '2023/02/13 08:00:00',
    create: '2023/04/13 08:00:00',
    planPowerGeneration: '发电20kwh',
    planPowerConsumption: '20kwh',
    status: '执行中',
  },
];
export const shceduleDetail = {
  scheduleId: 341,
  decisionId: 14,
  resourceId: 1129,
  startTime: '2024-01-06 00:00:00',
  endTime: '2024-01-06 23:59:59',
  resourceName: '负荷1129',
  status: '未开始',
  resourceType: '充电桩',
  totalLoadCut: 24.49,
  loadCutList: [
    0.8487385, 0.7841277, 0.78444856, 0.75862885, 0.776692, 0.8511396,
    0.99127895, 1.1155944, 1.1610281, 1.1684626, 1.1696607, 1.1739256,
    1.1396482, 1.11901, 1.0991962, 1.0954435, 1.09952, 1.1270612, 1.1767308,
    1.1494349, 1.0715882, 0.9924628, 0.945946, 0.8877443,
  ],
  totalCost: 18076.1,
  costList: [
    553.3289, 483.08347, 461.90793, 434.2016, 442.76843, 505.59406, 669.9749,
    830.1579, 972.4044, 977.90533, 937.1428, 914.3916, 837.4024, 799.8508,
    806.3169, 838.8402, 874.32404, 967.3666, 1007.6208, 959.4371, 833.8667,
    725.7833, 659.85944, 582.5532,
  ],
  totalIncome: 224193,
  incomeList: [
    6741.15, 6101.7783, 5682.5845, 5429.8394, 5498.294, 6265.4194, 8317.086,
    10287.649, 12071.252, 12118.824, 11636.325, 11328.781, 10398.42, 9909.678,
    10011.894, 10395.501, 10853.939, 11987.7295, 12511.103, 11888.547,
    10352.763, 8993.376, 8192.125, 7218.5737,
  ],
  totalProfit: 206117,
  profitList: [
    6187.8213, 5618.695, 5220.6763, 4995.6377, 5055.526, 5759.825, 7647.1113,
    9457.491, 11098.848, 11140.919, 10699.183, 10414.39, 9561.018, 9109.827,
    9205.577, 9556.661, 9979.615, 11020.362, 11503.481, 10929.109, 9518.8955,
    8267.593, 7532.2656, 6636.0205,
  ],
  transactionType: '日前',
  chargeList: [],
  chargeCurveJson: null,
  reserveList: [],
  reserveCurveJson: null,
};
