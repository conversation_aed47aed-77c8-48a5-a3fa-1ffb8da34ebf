import dayjs from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';
import ConnectLines from './components/ConnectLines';
import CustomTime from './components/CustomTime';
import DailyPrice from './components/DailyPrice';
import DirectlyElectrical from './components/DirectlyElectrical';
import DirectlyPublic from './components/DirectlyPublic';
import NewEnergy from './components/NewEnergy';
import PumpedStorage from './components/PumpedStorage';
import styles from './index.sass';

const LoadForecasting: React.FC = () => {
  const getTargetDate = useCallback(() => {
    const now = dayjs();
    const hour = now.hour();
    // 如果当前时间在10点前，显示当天
    if (hour < 10) {
      return now;
    }
    // 否则显示后一天
    return now.add(1, 'day');
  }, []);

  const [selectedDate, setSelectedDate] = useState(getTargetDate);
  const [lastUserSelectedDate, setLastUserSelectedDate] =
    useState<dayjs.Dayjs | null>(null);

  const handleDateChange = useCallback((date: dayjs.Dayjs) => {
    setSelectedDate(date);
    setLastUserSelectedDate(date);
  }, []);

  // 每分钟检查一次时间，只在日期需要更新时才更新状态
  useEffect(() => {
    const checkAndUpdateDate = () => {
      const targetDate = getTargetDate();
      // 如果用户选择了日期，且选择的日期不是今天或明天，则保持用户的选择
      if (
        lastUserSelectedDate &&
        !targetDate.isSame(lastUserSelectedDate, 'day')
      ) {
        return;
      }
      // 只有当目标日期与当前选择的日期不同时才更新
      if (!targetDate.isSame(selectedDate, 'day')) {
        setSelectedDate(targetDate);
      }
    };

    // 立即执行一次检查
    checkAndUpdateDate();

    // 设置定时器，每分钟检查一次
    const timer = setInterval(checkAndUpdateDate, 60000);

    return () => clearInterval(timer);
  }, [getTargetDate, selectedDate, lastUserSelectedDate]);

  return (
    <div className={styles.container}>
      <div className={styles.timePicker}>
        <CustomTime selectedDate={selectedDate} onChange={handleDateChange} />
      </div>
      <div className={styles.gridLayout}>
        <div className={styles.gridItem}>
          <DirectlyPublic selectedDate={selectedDate} />
        </div>
        <div className={styles.gridItem}>
          <DirectlyElectrical selectedDate={selectedDate} />
        </div>
        <div className={styles.gridItem}>
          <NewEnergy selectedDate={selectedDate} />
        </div>
        <div className={styles.gridItem}>
          <ConnectLines selectedDate={selectedDate} />
        </div>
        <div className={styles.gridItem}>
          <PumpedStorage selectedDate={selectedDate} />
        </div>
        <div className={styles.gridItem}>
          <DailyPrice selectedDate={selectedDate} />
        </div>
      </div>
    </div>
  );
};

export default LoadForecasting;
