import React, { useEffect, useState } from 'react';

import CustomStepButton from '@/components/CustomStepButton';
import TradeInPut from '@/components/TradeInput';
import { AGetNowApply, AUpdateNowApply } from '@/services/marketTrading';
import { history, useRequest } from '@umijs/max';
import { Form, Popconfirm, message } from 'antd';
import EditTitle from './EditTitle';
import InfoContent from './InfoContent';
import SalesChart from './SalesChart';
import styles from './index.sass';

interface Props {
  name?: string;

  onClickPrev: () => void;
}
const Index: React.FC<Props> = (props: Props) => {
  const { onClickPrev } = props;
  const [form] = Form.useForm();
  const { data, run } = useRequest(AGetNowApply, {
    manual: false,
    onSuccess: (successData) => {
      console.log('run成功');
      if (successData && successData.curveList) {
        const initialValues = {};
        successData.curveList.forEach((value, index) => {
          // 初始化每个输入的值
          initialValues[`curveValue${index}`] = value;
        });
        // 使用 form.setFieldsValue 来设置表单的初始值
        console.log('initialValues', initialValues);
        form.setFieldsValue(initialValues);
      }
    },
  });

  const validator = (rule: null, value) => {
    if (/^-?\d+(\.\d{1,2})?$/.test(value)) {
      if (value < 500) {
        return Promise.resolve(100);
      } else {
        return Promise.reject(new Error('请输入小于500的值'));
      }
    } else {
      return Promise.reject(new Error('请输入数字(保留两位小数)'));
    }
  };
  const onCheck = async () => {
    try {
      const values = await form.validateFields();
      AUpdateNowApply({
        modifiedNextDayCurveList: Object.values(values),
      }).then((res) => {
        run();
        if (res.success) {
          message.success('保存成功');
        }
      });
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
      message.error('验证失败，请检查输入');
    }
  };

  const hours = [...Array(24).keys()].map((hour) => `${hour}:00时刻报量`);
  // 准备xAxisData
  const xAxisData = hours.map((hour) => hour.split('时')[0]);
  console.log('data2', data);

  const [curveListState, setCurveListState] = useState<number[]>([]);

  useEffect(() => {
    if (data && data.curveList) {
      setCurveListState(data.curveList);
    }
  }, [data]);

  const onValuesChange = (changedValues, allValues) => {
    // 使用 hours 数组来确保顺序正确
    const newCurveList = hours.map((hour) => {
      const fieldName = `curveValue${hour.split(':')[0]}`; // 根据小时生成字段名
      return allValues[fieldName];
    });
    setCurveListState(newCurveList);
  };

  return (
    <Form form={form} layout="vertical" onValuesChange={onValuesChange}>
      <div className={styles.box}>
        <div className={styles.content}>
          <div className={styles.displayInfo}>
            <div className={styles.title}>
              <EditTitle title="交易策略信息" info="（报量单位 / MW）" />
            </div>
            <div className={styles.info}>
              {data?.curveList?.map((value, index) => (
                <Form.Item
                  key={index}
                  name={`curveValue${index}`}
                  rules={[{ validator }]}
                >
                  <TradeInPut
                    key={index}
                    left={hours[index]}
                    placeholder={`${value}`}
                    value={value}
                  />
                </Form.Item>
              ))}
            </div>
          </div>
          <div className={styles.displayChart}>
            <div className={styles.title}>
              <EditTitle
                title="售电曲线"
                info={<InfoContent status="当前状态：" infoName="  未申报" />}
              />
            </div>
            <div className={styles.chart}>
              {data && curveListState && (
                <SalesChart
                  xAxisData={xAxisData}
                  data={data}
                  curveListState={curveListState}
                  seriesDataArrays={[
                    data?.originalCurveList || [],
                    curveListState || [],
                  ]}
                />
              )}
            </div>
          </div>
        </div>
        <div className={styles.bottom}>
          <div className={styles.leftButton}>
            <CustomStepButton
              name="上一步"
              className={styles.stepButton}
              onClick={onClickPrev}
            ></CustomStepButton>
          </div>

          <div className={styles.rightButton}>
            <div className={styles.saveButton}>
              <CustomStepButton
                name="保存"
                onClick={onCheck}
              ></CustomStepButton>
            </div>
            <Popconfirm
              title="提示"
              description="关闭前请确认已保存修改"
              onConfirm={() => {
                history.push(
                  '/market-trading/market-trading?openNowDetail=true',
                );
              }}
              okText="确认"
              cancelText="取消"
            >
              <CustomStepButton
                name="关闭"
                className={styles.stepButton}
                // onClick={onClickNext}
              ></CustomStepButton>
            </Popconfirm>
          </div>
        </div>
      </div>
    </Form>
  );
};

export default Index;
