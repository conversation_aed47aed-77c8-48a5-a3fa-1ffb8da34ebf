@import '~@/assets/css/helpers.sass'
.stepsContainer
  margin-bottom: px(30)
  padding: px(20) px(30)
  background: linear-gradient(135deg, rgba(12, 67, 126, 0.3), rgba(29, 92, 158, 0.2))
  border-radius: px(12)
  border: 1px solid rgba(71, 114, 255, 0.3)
  backdrop-filter: blur(px(10))

.customSteps
  :global
    .ant-steps-item
      cursor: pointer
      transition: all 0.3s ease

      &:hover
        transform: translateY(-px(2))

      .ant-steps-item-icon
        background: linear-gradient(135deg, #4987D4, #2F5F8F)
        border-color: #4987D4
        color: #fff
        box-shadow: 0 px(4) px(12) rgba(73, 135, 212, 0.3)
        transition: all 0.3s ease

        &:hover
          box-shadow: 0 px(6) px(16) rgba(73, 135, 212, 0.5)
          transform: scale(1.1)

      .ant-steps-item-title
        color: #94EFFF !important
        font-weight: 600
        font-size: px(16)
        text-shadow: 0 0 px(8) rgba(148, 239, 255, 0.5)

      .ant-steps-item-description
        color: rgba(148, 239, 255, 0.8) !important
        font-size: px(14)

    .ant-steps-item-active
      .ant-steps-item-icon
        background: linear-gradient(135deg, #00D4FF, #0099CC) !important
        border-color: #00D4FF !important
        box-shadow: 0 px(6) px(20) rgba(0, 212, 255, 0.6) !important
        animation: pulse 2s infinite

      .ant-steps-item-title
        color: #00D4FF !important

    .ant-steps-item-finish
      .ant-steps-item-icon
        background: linear-gradient(135deg, #52C41A, #389E0D) !important
        border-color: #52C41A !important

      .ant-steps-item-title
        color: #52C41A !important

    .ant-steps-item-tail::after
      background: linear-gradient(90deg, #4987D4, #00D4FF)
      height: px(2)

@keyframes pulse
  0%
    box-shadow: 0 px(6) px(20) rgba(0, 212, 255, 0.6)
  50%
    box-shadow: 0 px(8) px(25) rgba(0, 212, 255, 0.8)
  100%
    box-shadow: 0 px(6) px(20) rgba(0, 212, 255, 0.6)

.stepContent
  margin-top: px(20)
  padding: px(20)
  background: linear-gradient(135deg, rgba(12, 67, 126, 0.2), rgba(29, 92, 158, 0.1))
  border-radius: px(8)
  border: 1px solid rgba(71, 114, 255, 0.2)
  min-height: px(300)
  width: 100%
  overflow-x: auto

  // 外层滚动容器样式
  div[style*="overflowY"]
    &::-webkit-scrollbar
      width: px(12)
      background: transparent

    &::-webkit-scrollbar-track
      background: linear-gradient(180deg, rgba(12, 67, 126, 0.1), rgba(29, 92, 158, 0.2))
      border-radius: px(8)
      border: 1px solid rgba(71, 114, 255, 0.1)
      box-shadow: inset 0 px(2) px(4) rgba(0, 0, 0, 0.1)

    &::-webkit-scrollbar-thumb
      background: linear-gradient(135deg, #4987D4, #2F5F8F, #1E4A73)
      border-radius: px(8)
      border: 1px solid rgba(255, 255, 255, 0.1)
      box-shadow: 0 px(2) px(8) rgba(73, 135, 212, 0.3), inset 0 px(1) px(2) rgba(255, 255, 255, 0.2)
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1)

      &:hover
        background: linear-gradient(135deg, #00D4FF, #0099CC, #006B99)
        box-shadow: 0 px(4) px(12) rgba(0, 212, 255, 0.5), inset 0 px(1) px(2) rgba(255, 255, 255, 0.3)
        transform: scaleX(1.1)

      &:active
        background: linear-gradient(135deg, #0088CC, #006699, #004466)
        box-shadow: 0 px(2) px(6) rgba(0, 136, 204, 0.6), inset 0 px(1) px(2) rgba(255, 255, 255, 0.1)
        transform: scaleX(0.95)

    &::-webkit-scrollbar-corner
      background: rgba(12, 67, 126, 0.1)
      border-radius: px(8)

    // 滚动条出现动画
    &:hover::-webkit-scrollbar-thumb
      animation: scrollbarGlow 2s ease-in-out infinite alternate

// 滚动条发光动画
@keyframes scrollbarGlow
  0%
    box-shadow: 0 px(4) px(12) rgba(0, 212, 255, 0.3), inset 0 px(1) px(2) rgba(255, 255, 255, 0.2)
  100%
    box-shadow: 0 px(6) px(20) rgba(0, 212, 255, 0.6), inset 0 px(1) px(2) rgba(255, 255, 255, 0.4)

.stepTitle
  color: #94EFFF
  font-size: px(20)
  font-weight: 600
  margin-bottom: px(20)
  text-shadow: 0 0 px(10) rgba(148, 239, 255, 0.5)
  border-bottom: 2px solid rgba(71, 114, 255, 0.3)
  padding-bottom: px(10)

.operationInfo
  color: #94EFFF
  font-size: px(16)
  line-height: 1.8

  p
    margin-bottom: px(10)
    padding: px(8) px(15)
    background: rgba(73, 135, 212, 0.2)
    border-radius: px(6)
    border-left: 3px solid #4987D4

.hourlyData
  color: #94EFFF
  font-size: px(16)
  line-height: 1.8

  p
    margin-bottom: px(10)
    padding: px(8) px(15)
    background: rgba(73, 135, 212, 0.2)
    border-radius: px(6)
    border-left: 3px solid #4987D4

.chart
  display: flex
  flex-direction: row
  width: 100%
  height: px(400)
  justify-content: space-between
  margin-bottom: px(20)

  // & > div
  //   height: 100%
  //   margin: px(10)
  //   background-image: url('@/assets/bg/settlementallocation/bg1.png')
  //   background-size: 100% 100%
  //   display: flex
  //   flex-direction: column
  //   align-items: center
.table
  width: 100% !important
  min-width: px(500) !important

  :global
    .ant-table
      width: 100% !important
      table-layout: fixed !important
.title
  font-size: px(16)
  text-shadow: 0 0 px(10) rgb(5, 88, 243), px(2) px(2) px(10) rgb(5, 88, 243)
  transform: translate(0, -px(10))
