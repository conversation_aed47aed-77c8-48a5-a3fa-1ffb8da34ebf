import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
import styles from './index.sass';
import { chartData } from './mock';
import { getOption } from './option';
export default function () {
  const container = useRef<HTMLDivElement>(null);
  const myChartRef = useRef<echarts.ECharts>();

  // 初始化图表
  useEffect(() => {
    if (container.current) {
      // 初始化ECharts实例
      const chart = echarts.init(container.current);
      myChartRef.current = chart;

      // 设置图表选项
      chart.setOption(getOption(chartData));

      // 窗口大小变化时自动调整图表大小
      const handleResize = () => {
        chart.resize();
      };
      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);

        chart.dispose();
      };
    }
  }, []);

  return <div className={styles.box} ref={container}></div>;
}
