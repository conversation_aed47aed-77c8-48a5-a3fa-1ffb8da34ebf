@import '~@/assets/css/helpers.sass'

.box
  width: 100%
  flex-grow: 1
  display: flex
  flex-direction: column

 
  
  .content
    margin-top: px(30)
    display: flex
    flex-direction: row
    justify-content: space-between
 
    .displayInfo
      align-items: start // 确保内容从顶部开始
      background: linear-gradient(135deg, rgb(37, 89, 160), rgba(22, 49, 90, 0.37))
      border-radius: px(15)
      width: px(750)
      height: 100%
      max-height: px(750)
      margin-right: px(30)
      .title
        margin-top: -px(10)
      .info
        max-height: px(700)
        display: grid
        padding-left: px(30)
        grid-template-columns: repeat(3, 1fr) // Creates two columns with equal width
        overflow-y: auto
        overflow-x: hidden
      .info::-webkit-scrollbar 
        width: px(8) 
      .info::-webkit-scrollbar-thumb  
        background-color: #031837
        border-radius: px(4) 
      .info::-webkit-scrollbar-track 
        background: #24569A


    .displayChart
      width: 100%
      height:100%
      background-color: #031837
      border-radius: px(15)
      .chart
        width: px(980)
        height:px(580)
        margin-left: px(30)
  .bottom
 
    display: flex
    justify-content: space-between
    align-items: center
    .leftButton
      position: absolute
      bottom: px(40)
      left: px(20)

    
    .rightButton
      position: absolute
      bottom: px(40)
      right: px(20)
      display: flex
      flex-direction: row
     
      .saveButton
 
        margin-right: px(20)
      
 
.ant-form-item 
  margin-bottom: px(0) 
 

/* 调整错误提示的样式 */
.ant-form-item-explain, .ant-form-item-extra  
  font-size: px(8) 
 

 

 

    
