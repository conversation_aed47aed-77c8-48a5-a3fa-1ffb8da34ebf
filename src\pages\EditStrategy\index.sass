@import '~@/assets/css/helpers.sass'

.box
  color: aliceblue
  display: flex
  flex-direction: column
  align-items: center

.step
  display: flex
  align-items: center
  margin-bottom: px(10)
.item
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center
  height: px(80)
  font-size: px(18)
.icon
  width: px(63)
  height: px(46)
  margin-bottom: px(5)
.activeIcon
  width: px(63)
  height: px(56)
  margin-bottom: px(5)
.bar0
  width: px(880)
  height: 1px
  background: linear-gradient(to right, rgba(138,187,244,1),rgba(21,105,205,1))
.bar1
  width: px(480)
  height: 1px
  background: linear-gradient(to right, rgba(21,105,205,1),rgba(15,83,155,1))
