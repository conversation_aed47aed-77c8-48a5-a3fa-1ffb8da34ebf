import CustomButton from '@/components/CustomButton';
import CustomLoad from '@/components/CustomLoad';
import {
  AComputeMarketApply,
  AGetMarketTimePair,
  AGetResponseStatus,
} from '@/services/demandResponseEdit';
import { history, useRequest } from '@umijs/max';
import { message } from 'antd';
import React, { useEffect, useState } from 'react';
import ResponseInfo from './ResponseInfo';
import ResponseResource from './ResponseResource';
import styles from './index.sass';

interface Props {
  onReturn: () => void;
  onNext: () => void;
}
const Index: React.FC<Props> = (prop: Props) => {
  const { onNext, onReturn } = prop;
  const { data: status } = useRequest(AGetResponseStatus);
  const searchParams = new URLSearchParams(location.search);
  const responseId = searchParams.get('id');
  const [open, setOpen] = useState(false);
  const { data: timeRange, run } = useRequest(AGetMarketTimePair, {
    manual: true,
  });
  const [change, setChange] = useState(0);
  useEffect(() => {
    // 获取时段
    run({ demandResponseId: responseId });
  }, []);
  const onChange = () => {
    setChange((last) => {
      return last + 1;
    });
  };
  const onCancel = () => {
    setOpen(false);
  };
  const handleNext = () => {
    if (status.isApplyed) {
      onNext();
    } else {
      AComputeMarketApply({
        demandResponseId: responseId,
      }).then(() => {
        onNext();
        message.success('计算成功！');
      });
    }
  };

  return (
    <div className={styles.box}>
      <div className={styles.right}>
        <ResponseResource
          timeRange={timeRange}
          status={status}
          onChange={onChange}
        ></ResponseResource>
      </div>
      <div className={styles.left}>
        <ResponseInfo change={change}></ResponseInfo>
      </div>
      <div className={styles.buttons}>
        <CustomButton
          className={styles.button}
          onClick={() => {
            history.push('/market-trading/demand-response');
          }}
        >
          关闭
        </CustomButton>
        <CustomButton
          className={styles.button}
          onClick={() => {
            onReturn();
          }}
        >
          上一步
        </CustomButton>
        {status?.isApplyed ? (
          <CustomButton onClick={handleNext}>查看报量报价信息</CustomButton>
        ) : (
          // <Popconfirm
          //   title="提示"
          //   okText="确认"
          //   cancelText="取消"
          //   description={
          //     <div>
          //       根据用户实际可调控容量，计算总的报量、报价，
          //       <br /> 并生成报量报价计划，
          //       <br />
          //       确认后将不能修改用户的可调容量。
          //     </div>
          //   }
          //   onConfirm={handleNext}
          //   onCancel={() => null}
          // >
          <CustomButton onClick={handleNext} className={styles.button}>
            计算报量报价信息
          </CustomButton>
          // </Popconfirm>
        )}
      </div>
      <CustomLoad open={open} onCancel={onCancel} />
    </div>
  );
};

export default Index;
