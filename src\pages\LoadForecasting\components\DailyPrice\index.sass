@import '@/assets/css/helpers.sass'

.select
    display: flex
    height: fit-content
    position: absolute 
    left: px(246)
    top: px(5)
.price
  width: 100%
  height: 100%
.value
  display: flex
  flex-direction: row
  justify-content: center
.max
    font-size: px(20)
    font-family: youshe
    line-height: px(40)
    color:#52C1FA
    margin-right: px(10)

.header
    position: relative
    display: flex
    align-items: center
    justify-content: space-between

.expandButton
    position: absolute
    top: px(8)
    right: px(15)
    background: rgba(255, 255, 255, 0.1)
    border: px(1) solid rgba(255, 255, 255, 0.3)
    color: white
    padding: px(4) px(8)
    border-radius: px(4)
    cursor: pointer
    font-size: px(16)
    display: flex
    align-items: center
    gap: px(4)
    transition: all 0.3s ease
    z-index: 10
    &:hover
        background: rgba(255, 255, 255, 0.2)
        border-color: rgba(255, 255, 255, 0.5)

.modalContent
    .modalHeader
        display: flex
        justify-content: space-between
        align-items: center
        margin-bottom: px(20)
        padding-bottom: px(15)
    .value
        display: flex
        flex-direction: row
    .max
        font-size: px(24)
        font-family: youshe
        line-height: px(40)
        color: #52C1FA
        margin-right: px(20)
    .dateRangeContainer
        display: flex
        align-items: center
        gap: px(10)
    .dateLabel
        color: white
        font-size: px(16)
        white-space: nowrap
    .dateRange
        :global
          .ant-picker
            width: px(260)
            padding: px(3) px(10) !important
            background: rgba(0,0,0,0)
            border: white solid px(1)
            line-height: 0
            height: px(34) !important
          .ant-picker-input >input,.ant-picker-separator,.ant-picker-suffix,
          .ant-picker .ant-picker-input >input::placeholder
            color: white !important
            font-size: px(16)
.modalChart
    width: 100%
    height: px(600)
