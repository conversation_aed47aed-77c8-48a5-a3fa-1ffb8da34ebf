import React from 'react';
import styles from './index.sass';

interface InfoContentProps {
  status: React.ReactNode;

  infoName: React.ReactNode;
}

const InfoContent: React.FC<InfoContentProps> = ({
  status,

  infoName,
}) => {
  return (
    <div className={styles.infoContent}>
      <div className={styles.devider}></div>
      <div className={styles.status}>{status}</div>
      <div className={styles.right}>
        <div className={styles.statusIcon}> </div>
        <div className={styles.infoName}>{infoName}</div>
      </div>
    </div>
  );
};

export default InfoContent;
