import { DatePicker, DatePickerProps } from 'antd';
import locale from 'antd/lib/date-picker/locale/zh_CN';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/zh-cn';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import React, { useEffect, useMemo, useState } from 'react';
import { disabledDate } from '../../util';
import styles from './index.sass';

dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.locale('zh-cn');

locale!.lang = {
  ...locale!.lang,
  monthFormat: 'M月',
  shortWeekDays: ['日', '一', '二', '三', '四', '五', '六'],
};

interface CustomInputProps extends Omit<DatePickerProps, 'onChange'> {
  onChange?: (date: Dayjs) => void;
  selectedDate?: Dayjs;
}

const CustomInput: React.FC<CustomInputProps> = ({
  onChange,
  selectedDate,
  ...props
}) => {
  const [currentTime, setCurrentTime] = useState(dayjs());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(dayjs());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const handleChange = (date: Dayjs | null) => {
    if (date && onChange) {
      onChange(date);
    }
  };

  const getDisabledDate = useMemo(() => {
    return (date: Dayjs) => disabledDate(date);
  }, [currentTime]);

  return (
    <DatePicker
      {...props}
      allowClear={false}
      locale={locale}
      value={selectedDate}
      format="YYYY-MM-DD"
      disabledDate={getDisabledDate}
      className={`${styles.box} ${props.className}`}
      onChange={handleChange}
    />
  );
};

export default CustomInput;
