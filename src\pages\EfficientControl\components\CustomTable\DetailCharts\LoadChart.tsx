import { IScheduleDetail } from '@/pages/EfficientControl/typing';
import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';

interface Prop {
  scheduleDetail: IScheduleDetail;
}
const LineChart = (prop: Prop) => {
  const { scheduleDetail } = prop;

  const chartRef = useRef(null);
  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    const { loadCutList, loadActualCutList, actualPowerRateCurveList } =
      scheduleDetail;
    const chartInstance = echarts.init(chartRef.current);
    const options = {
      legend: {
        data: ['预计负荷削减', '实际负荷削减', '实际功率曲线'],
        textStyle: {
          color: 'white',
          fontSize: px(16) + 'px',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
      },
      grid: {
        left: '2%',
        right: '2%',
        top: '10%',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            textStyle: {
              color: 'white',
              fontSize: px(16) + 'px',
            },
          },
          data: [
            '01:00',
            '02:00',
            '03:00',
            '04:00',
            '05:00',
            '06:00',
            '07:00',
            '08:00',
            '09:00',
            '10:00',
            '11:00',
            '12:00',
            '13:00',
            '14:00',
            '15:00',
            '16:00',
            '17:00',
            '18:00',
            '19:00',
            '20:00',
            '21:00',
            '22:00',
            '23:00',
            '24:00',
          ],
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: 'kW',
          smooth: true,
          nameTextStyle: {
            color: 'white',
            fontSize: px(16) + 'px',
          },
          axisLabel: {
            textStyle: {
              color: 'white',
              fontSize: px(16) + 'px',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.15)',
            },
          },
        },
      ],
      series: [
        {
          name: '预计负荷削减',
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 12,
          itemStyle: {
            color: 'rgb(61, 230, 255)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(54, 161, 255, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(25, 104, 255, 0)',
              },
            ]),
          },
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(61, 230, 255)',
              },
              {
                offset: 1,
                color: 'rgb(25, 104, 255)',
              },
            ]),
            width: px(5),
          },
          emphasis: {
            focus: 'series',
          },
          data: loadCutList,
        },
        {
          name: '实际负荷削减',
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 12,
          itemStyle: {
            color: 'rgb(255, 241, 0)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255, 241, 0, 0.41)',
              },
              {
                offset: 1,
                color: 'rgba(255, 241, 0, 0)',
              },
            ]),
          },
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(255, 241, 0)',
              },
              {
                offset: 1,
                color: 'rgb(255, 241, 0)',
              },
            ]),
            width: px(5),
          },
          emphasis: {
            focus: 'series',
          },
          data: loadActualCutList,
        },
        {
          name: '实际功率曲线',
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 12,
          itemStyle: {
            color: 'rgb(70, 224, 129)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(71, 255, 164, 0.41)',
              },
              {
                offset: 1,
                color: 'rgba(116, 168, 255, 0)',
              },
            ]),
          },
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(70, 224, 129)',
              },
              {
                offset: 1,
                color: 'rgb(13, 255, 183)',
              },
            ]),
            width: px(5),
          },
          emphasis: {
            focus: 'series',
          },
          data: actualPowerRateCurveList,
        },
      ],
    };
    chartInstance.setOption(options);

    window.addEventListener('resize', () => {
      chartInstance.resize();
    });

    return () => {
      window.removeEventListener('resize', () => {
        chartInstance.resize();
      });
      chartInstance.dispose();
    };
  }, [scheduleDetail]);

  return (
    <div
      ref={chartRef}
      className="lineChart"
      style={{ width: px(1150) + 'px', height: px(400) + 'px' }}
    />
  );
};

export default LineChart;
