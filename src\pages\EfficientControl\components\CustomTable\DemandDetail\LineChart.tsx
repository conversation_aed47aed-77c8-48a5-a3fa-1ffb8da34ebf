import px from '@/utils/px';
import { useModel } from '@umijs/max';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
interface Prop {
  data?: any;
}
const LineChart = (prop: Prop) => {
  const { data } = prop;
  const { baselineType } = useModel('efficient');
  console.log('data', data);
  const chartRef = useRef(null);
  useEffect(() => {
    if (!chartRef.current || !data) {
      return;
    }
    const { baseLineList, expectedList, realTimeList } = data;
    const chartInstance = echarts.init(chartRef.current);
    const options = {
      legend: {
        data: [baselineType, '预期执行曲线', '实际执行曲线'],
        textStyle: {
          color: 'white',
          fontSize: px(16) + 'px',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
      },
      grid: {
        left: '2%',
        right: '2%',
        top: '10%',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            textStyle: {
              color: 'white',
              fontSize: px(16) + 'px',
            },
          },
          data: [
            '00:15',
            '00:30',
            '00:45',
            '01:00',
            '01:15',
            '01:30',
            '01:45',
            '02:00',
            '02:15',
            '02:30',
            '02:45',
            '03:00',
            '03:15',
            '03:30',
            '03:45',
            '04:00',
            '04:15',
            '04:30',
            '04:45',
            '05:00',
            '05:15',
            '05:30',
            '05:45',
            '06:00',
            '06:15',
            '06:30',
            '06:45',
            '07:00',
            '07:15',
            '07:30',
            '07:45',
            '08:00',
            '08:15',
            '08:30',
            '08:45',
            '09:00',
            '09:15',
            '09:30',
            '09:45',
            '10:00',
            '10:15',
            '10:30',
            '10:45',
            '11:00',
            '11:15',
            '11:30',
            '11:45',
            '12:00',
            '12:15',
            '12:30',
            '12:45',
            '13:00',
            '13:15',
            '13:30',
            '13:45',
            '14:00',
            '14:15',
            '14:30',
            '14:45',
            '15:00',
            '15:15',
            '15:30',
            '15:45',
            '16:00',
            '16:15',
            '16:30',
            '16:45',
            '17:00',
            '17:15',
            '17:30',
            '17:45',
            '18:00',
            '18:15',
            '18:30',
            '18:45',
            '19:00',
            '19:15',
            '19:30',
            '19:45',
            '20:00',
            '20:15',
            '20:30',
            '20:45',
            '21:00',
            '21:15',
            '21:30',
            '21:45',
            '22:00',
            '22:15',
            '22:30',
            '22:45',
            '23:00',
            '23:15',
            '23:30',
            '23:45',
            '24:00',
          ],
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: 'kW',
          smooth: true,
          nameTextStyle: {
            color: 'white',
            fontSize: px(16) + 'px',
          },
          axisLabel: {
            textStyle: {
              color: 'white',
              fontSize: px(16) + 'px',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.15)',
            },
          },
        },
      ],
      series: [
        {
          name: baselineType,
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 12,
          itemStyle: {
            color: 'rgb(61, 230, 255)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(54, 161, 255, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(25, 104, 255, 0)',
              },
            ]),
          },
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(61, 230, 255)',
              },
              {
                offset: 1,
                color: 'rgb(25, 104, 255)',
              },
            ]),
            width: px(5),
          },
          emphasis: {
            focus: 'series',
          },
          data: baseLineList,
        },
        {
          name: '预期执行曲线',
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 12,
          itemStyle: {
            color: 'rgb(255, 241, 0)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255, 241, 0, 0.41)',
              },
              {
                offset: 1,
                color: 'rgba(255, 241, 0, 0)',
              },
            ]),
          },
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(255, 241, 0)',
              },
              {
                offset: 1,
                color: 'rgb(255, 241, 0)',
              },
            ]),
            width: px(5),
          },
          emphasis: {
            focus: 'series',
          },
          data: expectedList,
        },
        {
          name: '实际执行曲线',
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 12,
          itemStyle: {
            color: 'rgb(70, 224, 129)',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(71, 255, 164, 0.41)',
              },
              {
                offset: 1,
                color: 'rgba(116, 168, 255, 0)',
              },
            ]),
          },
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(70, 224, 129)',
              },
              {
                offset: 1,
                color: 'rgb(13, 255, 183)',
              },
            ]),
            width: px(5),
          },
          emphasis: {
            focus: 'series',
          },
          data: realTimeList,
        },
      ],
    };
    chartInstance.setOption(options);
  }, [data]);

  return (
    <div
      ref={chartRef}
      className="lineChart"
      style={{ width: px(1450) + 'px', height: px(400) + 'px' }}
    />
  );
};

export default LineChart;
