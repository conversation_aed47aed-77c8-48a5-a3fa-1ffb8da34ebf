import CustomTable from '@/components/CustomTable';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import styles from '../index.sass';

interface ProfitCalculationStepProps {
  data: any[];
}

const ProfitCalculationStep: React.FC<ProfitCalculationStepProps> = ({
  data,
}) => {
  // 第三步：收益计算表格列配置
  const columns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'timeStr',
      key: 'timeStr',
      width: 120,
      align: 'center',
    },
    {
      title: '整体调节量(MW)',
      dataIndex: 'wholeAdjustEnergy',
      key: 'wholeAdjustEnergy',
      width: 130,
      align: 'center',
    },
    {
      title: '特来电调节量(MW)',
      dataIndex: 'teldAdjustEnergy',
      key: 'teldAdjustEnergy',
      width: 140,
      align: 'center',
    },
    {
      title: '电表调节量(MW)',
      dataIndex: 'meterAdjustEnergy',
      key: 'meterAdjustEnergy',
      width: 130,
      align: 'center',
    },
    {
      title: '中长期电价(元/MWh)',
      dataIndex: 'longPrice',
      key: 'longPrice',
      width: 100,
      align: 'center',
    },
    {
      title: '实时电价(元/MWh)',
      dataIndex: 'normalRealTimePrice',
      key: 'normalRealTimePrice',
      width: 100,
      align: 'center',
    },
    {
      title: '整体收益(元)',
      dataIndex: 'wholeProfit',
      key: 'wholeProfit',
      width: 100,
      align: 'center',
    },
    {
      title: '特来电收益(元)',
      dataIndex: 'teldProfit',
      key: 'teldProfit',
      width: 110,
      align: 'center',
    },
    {
      title: '电表收益(元)',
      dataIndex: 'meterProfit',
      key: 'meterProfit',
      width: 100,
      align: 'center',
    },
    {
      title: 'VPP收益(元)',
      dataIndex: 'vppProfit',
      key: 'vppProfit',
      width: 100,
      align: 'center',
    },
  ];

  return (
    <div className={styles.stepContent}>
      <h3 className={styles.stepTitle}>收益计算</h3>
      <div
        style={{
          width: '100%',
          minWidth: '500px',
          maxHeight: '400px',
          overflowY: 'scroll',
        }}
      >
        <CustomTable
          dataSource={data}
          columns={columns}
          className={styles.table}
          size="small"
          pagination={false}
        />
      </div>
    </div>
  );
};

export default ProfitCalculationStep;
