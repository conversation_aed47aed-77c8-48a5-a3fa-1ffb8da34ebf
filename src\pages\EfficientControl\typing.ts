export interface ISourceUseCount {
  countList: number[];
  resourceNameList: string[];
}
export interface IResourceSchedule {
  scheduleId: string;
  decisionId: string;
  resourceId: string;
  planPowerGeneration: string;
  planPowerConsumption: string;
  startTime: string;
  endTime: string;
  resourceName: string;
  resourceType: string;
  status: string;
  totalCost: string;
  totalIncome: string;
  totalLoadCut: string;
  totalProfit: string;
  transactionType: string;
}
export interface IResourceScheduleList {
  current: 1;
  list: IResourceSchedule[];
  pageSize: 10;
  total: 7;
}
export interface IScheduleDetail {
  loadCutList: number[];
  totalCost: number;
  costList: number[];
  totalIncome: number;
  incomeList: number[];
  totalProfit: number;
  profitList: number[];
  reserveList: number[];
  chargeList: number[];
  loadActualCutList: number[];
  costActualList: number[];
  profitActualList: number[];
  actualPowerRateCurveList: number[];
}
