import CustomModal from '@/components/CustomModal';
import px from '@/utils/px.js';
import React, { useEffect } from 'react';
import styles from './index.sass';

interface Props {
  name?: string;
  open: boolean;
  onCancel: () => void;
}

const Index: React.FC<Props> = (props: Props) => {
  const { open, onCancel } = props;
  useEffect(() => {}, []);
  const data = [
    {
      name: '接收指令',
      time: '2023/01/01 08:00:00',
    },
    {
      name: '开始执行',
      time: '2023/01/01 08:00:00',
    },
    {
      name: '执行中断',
      time: '2023/01/01 08:00:00',
    },
    {
      name: '继续执行',
      time: '2023/01/01 08:00:00',
    },
    {
      name: '执行完成',
      time: '2023/01/01 08:00:00',
    },
  ];
  return (
    <CustomModal
      open={open}
      title="指令执行详情"
      onCancel={onCancel}
      footer={null}
      className={styles.box}
      width={px(1200)}
      centered={true}
    >
      <div className={styles.process}>
        <div className={styles.title}>执行流程：</div>
        {data.map(({ name }, index) => {
          return (
            <div key={name} className={styles.processItem}>
              <div className={styles.processValue}>{name}</div>
              {index < data.length - 1 && <div className={styles.arrow}></div>}
            </div>
          );
        })}
      </div>
      <div className={styles.time}>
        <div className={styles.title}>执行时间：</div>
        {data.map(({ time, name }) => {
          return (
            <div key={name} className={styles.timeItem}>
              <div className={styles.timeBar}></div>
              <div className={styles.timeDot}></div>
              <div className={styles.timeValue}>{time}</div>
            </div>
          );
        })}
      </div>
    </CustomModal>
  );
};

export default Index;
