import CustomDate from '@/components/CustomDate';
import Tab from '@/components/Tab';
import { getQuantityAndPriceUsingGET } from '@/services/marketSituation2FController';

import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef, useState } from 'react';
import styles from './index.sass';
import { chartData as mockData } from './mock';
import { getOption } from './option';
const Index = () => {
  const container = useRef<HTMLDivElement>(null);
  const myChartRef = useRef<echarts.ECharts>();
  const [selectedDate, setSelectedDate] = useState<string>('2025-06-02');

  // Handle date selection
  const handleDateChange = (date: any) => {
    // Format the date as YYYY-MM-DD
    const formattedDate = dayjs(date).format('YYYY-MM-DD');
    console.log('Formatted date:', formattedDate);
    setSelectedDate(formattedDate);
  };

  //Initialize chart once when component mounts
  useEffect(() => {
    if (container.current && !myChartRef.current) {
      // Initialize ECharts instance
      const chart = echarts.init(container.current);
      myChartRef.current = chart;

      // Set initial data
      chart.setOption(getOption(mockData));

      // Add resize listener
      const handleResize = () => {
        chart.resize();
      };
      window.addEventListener('resize', handleResize);

      // Cleanup function
      return () => {
        window.removeEventListener('resize', handleResize);
        chart.dispose();
        myChartRef.current = undefined;
      };
    }
  }, []);

  // Fetch data when selected date changes
  useEffect(() => {
    if (myChartRef.current) {
      // Show loading state
      myChartRef.current.showLoading();

      // Fetch data from API
      getQuantityAndPriceUsingGET({
        dateStr: selectedDate,
      })
        .then((response) => {
          if (response.success && response.data) {
            console.log('API data:', response.data);

            // Update chart with API data
            if (myChartRef.current) {
              myChartRef.current.setOption(getOption(response.data));
              myChartRef.current.hideLoading();
            }
          } else {
            // Fallback to mock data if API fails
            console.log('Using mock data');
            if (myChartRef.current) {
              myChartRef.current.setOption(getOption(mockData));
              myChartRef.current.hideLoading();
            }
          }
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
          // Fallback to mock data if API fails
          if (myChartRef.current) {
            myChartRef.current.setOption(getOption(mockData));
            myChartRef.current.hideLoading();
          }
        });
    }
  }, [selectedDate]);

  return (
    <>
      <div className={styles.top}>
        <Tab title={'机组24小时电量与电价趋势图'}></Tab>
        <div className={styles.select}>
          <CustomDate
            defaultValue={dayjs('2025-06-02')}
            onChange={handleDateChange}
            format="YYYY-MM-DD"
          />
        </div>
      </div>
      <div className={styles.box} ref={container}></div>
    </>
  );
};

export default Index;
