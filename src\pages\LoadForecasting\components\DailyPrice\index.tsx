import CustomModal from '@/components/CustomModal';
import Tab from '@/components/Tab';
import px from '@/utils/px';
import { ExpandOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import styles from './index.sass';
import LineChart from './LineChart';
import MulitiDateChart from './MulitiDateChart';
interface Props {
  selectedDate: dayjs.Dayjs;
}

const DailyPrice: React.FC<Props> = ({ selectedDate }) => {
  const { maxPrice, minPrice } = useModel('forecast');
  const [modalOpen, setModalOpen] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(
    null,
  );

  const handleExpandClick = () => {
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
  };

  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  return (
    <>
      <div className={styles.header}>
        <Tab title="现货价格预测(元/MWh)"></Tab>
        <button
          type="button"
          className={styles.expandButton}
          onClick={handleExpandClick}
        >
          <ExpandOutlined /> 放大
        </button>
      </div>
      <div className={styles.value}>
        <div className={styles.max}>最小差值:{minPrice}</div>
        <div className={styles.max}>最大差值:{maxPrice}</div>
      </div>
      <LineChart selectedDate={selectedDate} />

      <CustomModal
        open={modalOpen}
        title="现货价格预测(元/MWh) - 详细视图"
        onCancel={handleModalClose}
        footer={null}
        width={px(1200)}
        centered={true}
      >
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <div className={styles.dateRangeContainer}>
              <span className={styles.dateLabel}>时间范围：</span>
              <div className={styles.dateRange}>
                <DatePicker.RangePicker
                  allowClear={false}
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  format="YYYY-MM-DD"
                  placeholder={['开始日期', '结束日期']}
                />
              </div>
            </div>
          </div>
          <div className={styles.modalChart}>
            {dateRange ? (
              <MulitiDateChart selectedDate={dateRange} />
            ) : (
              <LineChart selectedDate={selectedDate} />
            )}
          </div>
        </div>
      </CustomModal>
    </>
  );
};

export default DailyPrice;
