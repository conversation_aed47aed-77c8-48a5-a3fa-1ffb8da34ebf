import CustomSelect from '@/pages/LoadForecasting/components/CustomSelect';
import { useModel } from '@umijs/max';
import React from 'react';
import styles from './index.sass';

const Index: React.FC = () => {
  const { baselineType, setBaselineType } = useModel('efficient');
  //切换资源类型
  const onModelChange = (value: string) => {
    setBaselineType(value);
  };
  return (
    <div className={styles.select}>
      <CustomSelect
        value={baselineType}
        onChange={onModelChange}
        options={[
          { value: '相同日基线', label: '相同日基线' },
          { value: '连续日基线', label: '连续日基线' },
        ]}
      />
    </div>
  );
};

export default Index;
