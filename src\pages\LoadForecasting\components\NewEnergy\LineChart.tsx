import '@/assets/css/helpers.sass';
import services from '@/services/decisionSupport';
import px from '@/utils/px';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import { useEffect, useRef, useState } from 'react';
import styles from './index.sass';

interface Props {
  selectedDate: dayjs.Dayjs;
}

const Price: React.FC<Props> = ({ selectedDate }) => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>(null);
  const { setMaxPv, setMinPv, setMaxWind, setMinWind } = useModel('forecast');
  const { getNewEnergy } = services.loadForecastController;
  useEffect(() => {
    const dateStr = selectedDate.format('YYYY-MM-DD');
    getNewEnergy({ dateStr }).then((res) => {
      setData(res.data);
      console.log('xxxcvers', res.data);
      setMaxPv(res.data.pvMaxDiff);
      setMinPv(res.data.pvMinDiff);
      setMaxWind(res.data.windMaxDiff);
      setMinWind(res.data.windMinDiff);
    });
  }, [selectedDate]);
  useEffect(() => {
    if (!chartRef.current || !data) {
      return;
    }
    const chartInstance = echarts.init(chartRef.current);

    // 配置项和数据
    const options = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['D日风电出力', 'D-1日风电出力', 'D日光伏出力', 'D-1日光伏出力'],
        textStyle: { color: '#fff', fontSize: px(16) },
        left: 'center',
        right: 'center',
      },
      grid: {
        left: px(10),
        right: px(22),
        top: px(60),
        bottom: px(0),
        containLabel: true,
      },
      dataZoom: [
        {
          type: 'inside', // 或 'inside'
          start: 0, // 开始位置（0-100）
          end: 100,
        },
      ],

      xAxis: {
        boundaryGap: false,
        type: 'category',
        data: data.timeList,
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
          showMaxLabel: true, // 显示最后一个标签
          interval: 8.5,
        },
      },
      yAxis: {
        type: 'value',
        /*         name: data.unit,
        nameTextStyle: { color: '#fff', fontSize: px(16) }, */
        axisLabel: {
          textStyle: { color: '#fff', fontSize: px(16) },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
          },
        },
      },
      series: [
        {
          name: 'D日风电出力',
          type: 'line',
          smooth: true,
          data: data.wind1,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#ff1515',
          },
          lineStyle: {
            color: '#ff1515',
            width: px(3),
          },
        },
        {
          name: 'D-1日风电出力',
          type: 'line',
          smooth: true,
          data: data.wind0,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#006ebf',
          },
          lineStyle: {
            color: '#006ebf',
            width: px(3),
          },
        },
        {
          name: 'D日光伏出力',
          type: 'line',
          smooth: true,
          data: data.pv1,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#ff1515',
          },
          lineStyle: {
            color: '#ff1515',
            width: px(3),
            type: 'dashed',
          },
        },
        {
          name: 'D-1日光伏出力',
          type: 'line',
          smooth: true,
          data: data.pv0,
          showSymbol: false,
          symbolSize: px(10),
          itemStyle: {
            color: '#006ebf',
          },
          lineStyle: {
            color: '#006ebf',
            width: px(3),
            type: 'dashed',
          },
        },
      ],
    };
    chartInstance.setOption(options);
  }, [data]);

  return <div ref={chartRef} className={styles.price} />;
};

export default Price;
