// 5.19-5.26七天数据
export const chartData = {
  timePoints: ['5.19', '5.20', '5.21', '5.22', '5.23', '5.24', '5.25', '5.26'],
  // 电量数据（柱状图）
  governmentAuthorizedPower: [320, 350, 380, 410, 440, 470, 500, 530],
  yinDongBilateralPower: [220, 280, 340, 400, 460, 480, 500, 520],
  yinDongBiddingPower: [150, 210, 270, 330, 390, 450, 510, 570],
  bilateralTransactionPower: [120, 180, 240, 300, 360, 380, 400, 420],
  biddingTransactionPower: [420, 470, 520, 570, 620, 670, 720, 770],
  listingTransactionPower: [620, 700, 780, 860, 940, 1020, 1100, 1180],
  matchingTransactionPower: [520, 600, 680, 760, 840, 920, 1000, 1080],

  // 电价数据（折线图）
  dayAheadClearingPrice: [410, 430, 450, 470, 490, 510, 530, 550],
  realTimeClearingPrice: [400, 420, 440, 460, 480, 500, 520, 540],
  realTimeAveragePrice: [405, 425, 445, 465, 485, 505, 525, 545],

  // 额外电量指标（折线图）
  totalMediumLongTermPower: [1020, 1100, 1180, 1260, 1340, 1420, 1500, 1580],
  dayAheadClearingPower: [900, 980, 1060, 1140, 1160, 1180, 1200, 1220],
  realTimePowerUsage: [920, 960, 1000, 1040, 1080, 1120, 1160, 1200],
  actualPowerUsage80Percent: [736, 768, 800, 832, 864, 896, 928, 960],
};
