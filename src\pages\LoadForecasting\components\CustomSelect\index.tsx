import { Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import styles from './index.sass';

interface Props {
  onChange?: (dates: dayjs.Dayjs[]) => void;
  value?: dayjs.Dayjs[];
}

const CustomSelect: React.FC<Props> = ({ onChange, value }) => {
  const [options, setOptions] = useState<{ label: string; value: string }[]>(
    [],
  );

  useEffect(() => {
    const endDate = dayjs('2025-04-20');
    const startDate = dayjs('2025-05-31');
    const dateOptions = [];

    let currentDate = startDate;
    while (currentDate.isAfter(endDate) || currentDate.isSame(endDate, 'day')) {
      dateOptions.push({
        label: currentDate.format('YYYY-MM-DD'),
        value: currentDate.format('YYYY-MM-DD'),
      });
      currentDate = currentDate.subtract(1, 'day');
    }

    setOptions(dateOptions);
  }, []);

  return (
    <Select
      mode="multiple"
      className={styles.box}
      placeholder="请选择日期"
      onChange={(values) => {
        if (onChange) {
          onChange(values.map((v) => dayjs(v)));
        }
      }}
      value={value?.map((v) => v.format('YYYY-MM-DD'))}
      options={options}
      maxTagCount="responsive"
      listHeight={256}
      allowClear
    />
  );
};

export default CustomSelect;
