import { ALogin } from '@/services/login';
import { Button, Form, Input } from 'antd';
import styles from './index.sass';

export default function Index() {
  const [form] = Form.useForm();
  const { validateFields } = form;

  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const login = async () => {
    const values = await validateFields();
    ALogin(values)
      .then((res) => {
        localStorage.setItem('token', res.jwtToken);
        if (res.availablePageList[0] === 'decision-support') {
          location.href = '/decision-support/load-forecasting';
        } else {
          location.href = res.availablePageList[0];
        }
      })
      .catch(() => {
        localStorage.setItem('token', '');
      });
  };
  return (
    <div className={styles.box}>
      <div className={styles.container}>
        <div className={styles.title}>华电山东虚拟电厂协同管控平台</div>
        <Form form={form}>
          <Form.Item
            {...formItemLayout}
            name="name"
            label=""
            rules={[{ required: true, message: '请输入账号' }]}
          >
            <Input
              className={styles.input}
              placeholder="请输入账号"
              prefix={<img className={styles.img} src="/user.png" />}
            ></Input>
          </Form.Item>
          <Form.Item
            {...formItemLayout}
            name="pwd"
            label=""
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<img className={styles.img} src="/password.png" />}
              placeholder="请输入密码"
            />
          </Form.Item>
        </Form>
        <Button onClick={login}>登录</Button>
      </div>
    </div>
  );
}
