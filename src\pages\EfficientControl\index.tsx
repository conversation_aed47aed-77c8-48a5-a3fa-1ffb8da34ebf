import Tab from '@/components/Tab';
import React from 'react';
import SelectMode from './components/SelectMode';

import { useModel } from '@umijs/max';
import CurrentInfo from './components/CurrentInfo';
import CustomTable from './components/CustomTable';
import SumChart from './components/SumChart';
import styles from './index.sass';

interface Props {
  name: string;
}

const EfficientControl: React.FC<Props> = () => {
  const { unitType } = useModel('unitType');
  return (
    <div className={styles.box}>
      <div className={styles.left}>
        <div className={styles.bidder}>
          <SumChart></SumChart>
        </div>
        <div className={styles.state}>
          <Tab title={`${unitType}调度接口`}></Tab>
          {/* <StateChart></StateChart> */}
        </div>
      </div>
      <div className={styles.right}>
        <div className={styles.rightTop}>
          <SelectMode></SelectMode>
          <CurrentInfo></CurrentInfo>
        </div>
        <div className={styles.rightBottom}>
          <CustomTable></CustomTable>
        </div>
      </div>
    </div>
  );
};

export default EfficientControl;
