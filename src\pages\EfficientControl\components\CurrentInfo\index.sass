@import '@/assets/css/helpers.sass'
$bdcolor: #4987D4
$bdwidth: px(4)
.box
  display: flex
  flex-direction: row
  font-size: px(18)
  color: white
  background: linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to bottom,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,linear-gradient(to top,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left top,linear-gradient(to right,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) left bottom,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right top,linear-gradient(to left,$bdcolor 0px,$bdcolor $bdwidth,transparent $bdwidth) right bottom,
  background-size: px(20) px(20)
  background-repeat: no-repeat
  background-color: rgba(13, 44, 91, 0.2)
  box-shadow: inset 0 0 px(4) 2px #2F60A1
  width: fit-content
  height: px(150)
  padding: px(20) px(60)

.completeTitle
  width: px(210)
  background-image: url('@/assets/bg/efficientcontrol/bg5.png')
  height: px(40)
  background-size: 100% 100%
  text-align: right
  font-weight: bolder

.proportionTitle
  width: px(270)
  background-image: url('@/assets/bg/efficientcontrol/bg4.png')
  height: px(40)
  background-size: 100% 100%
  text-align: right
  font-weight: bolder

.completeBox
  width: px(120)
  height: px(120)

.proportionBox
  width: px(120)
  height: px(120)
