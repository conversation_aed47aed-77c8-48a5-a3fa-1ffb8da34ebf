import { APermission } from '@/services/login';
import { allPage } from '@/utils/util';
import { useEffect, useState } from 'react';
import styles from './index.sass';

export default function Index() {
  const [pageList, setPageList] = useState<string[]>([]);
  useEffect(() => {
    APermission().then((res) => {
      setPageList(res.availablePageList);
    });
  }, []);

  return (
    <div className={styles.box}>
      <img src="/per3.png" className={styles.img} alt="" />
      <div>
        <div>对不起，您没有权限访问该页面。</div>
        <div className={styles.page}>
          您可以访问
          <div className={styles.pageName}>
            {pageList?.map((res, index) => {
              return (
                <div key={res}>
                  {allPage[res]}
                  {index < pageList.length - 1 ? '、' : ''}
                </div>
              );
            })}
          </div>
          页面
        </div>
      </div>
    </div>
  );
}
