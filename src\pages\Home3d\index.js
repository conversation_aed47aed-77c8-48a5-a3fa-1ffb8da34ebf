import glbModelUrl from '@/assets/model/home.glb';
import px from '@/utils/px';
import { useGLTF } from '@react-three/drei';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Bloom, EffectComposer } from '@react-three/postprocessing';
import { history } from '@umijs/max';
import { Spin } from 'antd';
import classNames from 'classnames';
import { Suspense, useRef, useState } from 'react';
import * as THREE from 'three';
import { SetCamera } from './camera';
import styles from './index.sass';

const moduleNames = [
  '辅助决策',
  '市场交易',
  '结算分配',
  '首页',
  '基本信息',
  '高效调控',
];

const moduleClassNames = [
  {
    name: '辅助决策',
    className: styles.assistantDecision,
    top: px(60),
    left: px(800),
    path: '/decision-support/load-forecasting',
    content: { Top: px(10), left: px(790), width: px(300), height: px(388) },
    rightDes: { Top: px(120), left: px(1050), width: px(220), height: px(298) },
    desContent:
      '辅助决策模块通过详细资源信息和预测技术，选定优化调度模式，生成成本和市场调度策略，指导高效调控。',
  },
  {
    name: '市场交易',
    className: styles.marketTransaction,
    top: px(370),
    left: px(1253),
    path: '/market-trading',
    content: { Top: px(430), left: px(1220), width: px(320), height: px(257) },
    rightDes: { Top: px(430), left: px(1490), width: px(220) },
    desContent:
      '通过竞价机制，将辅助决策的市场调度策略转化为实际交易，促进资源有效配置。',
  },
  {
    name: '结算分配',
    className: styles.settlementDistribution,
    top: px(100),
    left: px(1300),
    path: '/settlement-allocation',
    content: { Top: px(160), left: px(1310), width: px(240), height: px(190) },
    rightDes: { Top: px(160), left: px(1550), width: px(220) },
    desContent:
      '汇总市场交易和资源调度的收益，按实际表现进行收益结算和分配，保障公平性。',
  },
  {
    name: '首页',
    className: styles.dataMonitoring,
    path: '/resource-monitor',
    top: px(370),
    left: px(370),
    content: { Top: px(420), left: px(325), width: px(320), height: px(270) },
    rightDes: { Top: px(430), left: px(605), width: px(220) },
    desContent:
      '对注册资源进行实时监控，反馈设备执行情况，确保数据的即时性和准确性。',
  },
  {
    name: '基本信息',
    className: styles.resourceManagement,
    path: '/resource-management',
    top: px(510),
    left: px(805),
    content: { Top: px(560), left: px(780), width: px(290), height: px(270) },
    rightDes: { Top: px(570), left: px(1050), width: px(220) },
    desContent:
      '用户在此注册资源，为平台提供管理和监测的基础数据，是资源整合的起点。',
  },
  {
    name: '高效调控',
    className: styles.efficientControl,
    path: '/efficient-control',
    top: px(90),
    left: px(300),
    content: { Top: px(150), left: px(320), width: px(250), height: px(200) },
    rightDes: { Top: px(150), left: px(550), width: px(220) },
    desContent:
      '接收调度策略，生成执行指令，确保终端设备按优化策略运行，提高调度效率。',
  },
];

const moduleLightMap = {
  辅助决策: { lightPosition: [0, 0.15, -0.08], lightIntensity: 0.08 },
  市场交易: { lightPosition: [0.34, 0.17, 0.29], lightIntensity: 0.04 },
  结算分配: { lightPosition: [0.53, 0.16, -0.1], lightIntensity: 0.04 },
  高效调控: { lightPosition: [-0.54, 0.13, -0.18], lightIntensity: 0.08 },
  首页: { lightPosition: [-0.34, 0.17, 0.29], lightIntensity: 0.035 },
  基本信息: { lightPosition: [0, 0.16, 0.33], lightIntensity: 0.06 },
};

// 优化后的Model组件
const Model = ({
  glbModelUrl,
  targetEmissiveIntensity,
  setIsLoading,
  setProgress,
}) => {
  const { scene } = useThree();
  const gltf = useGLTF(glbModelUrl, true, true, (loader) => {
    console.log(loader);

    loader.manager.onStart = () => {
      setIsLoading(true);
    };
    loader.manager.onLoad = () => {
      setIsLoading(false);
    };
    loader.manager.onProgress = (url, itemsLoaded, itemsTotal) => {
      // setProgress((itemsLoaded / itemsTotal) * 100);
      let progress = Math.floor((itemsLoaded / itemsTotal) * 100);
      setProgress(progress);
    };
  });

  useFrame(() => {
    gltf.scene.traverse((child) => {
      if (child.isMesh && child.name.includes('箭头')) {
        child.material.emissiveIntensity = 0.5;
        child.material.emissive = new THREE.Color('#FDFEB5'); // 假设你想要的常亮颜色是白色
      }
      // if (child.isMesh && child.name.includes('数据文字')) {
      //   child.material.emissiveIntensity = 0.5;
      //   child.material.emissive = new THREE.Color('#A3FFFF'); // 假设你想要的常亮颜色是白色
      // }
    });
  });

  return <primitive object={gltf.scene} position={[0, 0.08, 0]} />;
};
// 合并并优化后的PointLightWithTransition组件
const PointLightWithTransition = ({ activeModule }) => {
  const pointLightRef = useRef();

  const targetIntensity = activeModule
    ? moduleLightMap[activeModule].lightIntensity
    : 0;

  return (
    <pointLight
      ref={pointLightRef}
      position={
        activeModule ? moduleLightMap[activeModule].lightPosition : [0, 0, 0]
      }
      intensity={targetIntensity}
    />
  );
};

const App = () => {
  const [activeModule, setActiveModule] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [progressList, setProgressList] = useState([]);
  const updateProgress = (newProgress) => {
    setProgressList((currentList) => {
      // 添加新进度到数组中，如果该进度已存在，则不重复添加
      const newList = currentList.includes(newProgress)
        ? currentList
        : [...currentList, newProgress];
      return newList;
    });
  };

  return (
    <div
      style={{ cursor: activeModule ? 'pointer' : 'default' }}
      className={styles.container}
    >
      {isLoading && (
        <div className={styles.loading}>
          <Spin
            size="large"
            tip={
              <div className={styles.loadingText}>
                首页3D模型加载中...{Math.max(...progressList, 0)}%
              </div>
            }
          >
            {' '}
          </Spin>
        </div>
      )}
      {!isLoading &&
        moduleClassNames.map((module) => (
          <div key={module.name + 'ff'}>
            <div
              key={module.name}
              className={classNames(
                styles.title,
                activeModule === module.name ? styles.animateIn : styles.hidden,
              )}
              style={{
                top: module.top,
                left: module.left,
              }}
            >
              {module.name}
            </div>
            <div
              className={styles.content}
              style={{
                top: module.content.Top,
                left: module.content.left,
                width: module.content.width,
                height: module.content.height,
              }}
              onMouseEnter={() => setActiveModule(module.name)}
              onMouseLeave={() => setActiveModule(undefined)}
              onClick={() => {
                history.push(module.path);
              }}
              key={module.name + 'content'}
            ></div>
            <div
              className={classNames(
                styles.rightDes,
                activeModule === module.name
                  ? styles.animateInRight
                  : styles.hidden,
              )}
              style={{
                top: module?.rightDes?.Top,
                left: module?.rightDes?.left,
                width: module?.rightDes?.width,
                height: module?.rightDes?.height,
              }}
              key={module.name + 'right'}
            >
              <div className={styles.desTitle}>模块介绍</div>
              <div className={styles.desContent}>{module.desContent}</div>
            </div>
          </div>
        ))}

      <Canvas>
        <ambientLight intensity={4} />
        <PointLightWithTransition activeModule={activeModule} />
        <directionalLight position={[0, 5, 0]} />
        <SetCamera />
        <Suspense fallback={null}>
          <Model
            glbModelUrl={glbModelUrl}
            targetEmissiveIntensity={activeModule ? 0.5 : 0}
            setActiveModule={setActiveModule}
            setIsLoading={setIsLoading}
            setProgress={updateProgress}
          />
        </Suspense>
        <EffectComposer>
          <Bloom
            // luminanceThreshold={0.3}
            // luminanceSmoothing={0.9}
            height={300}
          />
        </EffectComposer>
      </Canvas>
    </div>
  );
};

export default App;
