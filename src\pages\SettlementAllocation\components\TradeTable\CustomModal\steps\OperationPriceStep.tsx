import px from '@/utils/px';
import { ConfigProvider, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import styles from './index.sass';
interface OperationPriceStepProps {
  operationData: any[];
  priceData: any[];
}

const OperationPriceStep: React.FC<OperationPriceStepProps> = ({
  operationData,
  priceData,
}) => {
  // 开停机表格列配置
  const operationColumns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
    },
    {
      title: '开停机状态',
      dataIndex: 'flag',
      key: 'flag',
      align: 'center',
    },
  ];

  // 电价表格列配置
  const priceColumns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
    },
    {
      title: '实时电价(元/MWh)',
      dataIndex: 'price',
      key: 'price',
      align: 'center',
    },
    {
      title: '中长期电价(元/MWh)',
      dataIndex: 'longPrice',
      key: 'longPrice',
      align: 'center',
    },
  ];

  return (
    <div className={styles.stepContent}>
      <h3 className={styles.stepTitle}>开停机与电价信息</h3>

      {/* 开停机信息表格 */}
      <div style={{ marginBottom: '20px' }}>
        <h4 style={{ margin: '10px 0', fontSize: '14px' }}>开停机信息</h4>
        <ConfigProvider
          theme={{
            components: {
              Table: {
                stickyScrollBarBg: '#f0f0f0', // 设置为更明亮的颜色
              },
            },
          }}
        >
          <Table
            dataSource={operationData}
            columns={operationColumns}
            className={styles.table}
            size="small"
            pagination={false}
            scroll={{ y: px(200) }}
          />
        </ConfigProvider>
      </div>

      {/* 电价信息表格 */}
      <div>
        <h4 style={{ margin: '10px 0', fontSize: '14px' }}>电价信息</h4>
        <ConfigProvider
          theme={{
            components: {
              Table: {
                stickyScrollBarBg: '#f0f0f0', // 设置为更明亮的颜色
              },
            },
          }}
        >
          <Table
            dataSource={priceData}
            columns={priceColumns}
            className={styles.table}
            size="small"
            pagination={false}
            scroll={{ y: px(200) }}
          />
        </ConfigProvider>
      </div>
    </div>
  );
};

export default OperationPriceStep;
