import CustomTable from '@/components/CustomTable';
import { ColumnsType } from 'antd/es/table';
import React from 'react';
import styles from '../index.sass';

interface OperationPriceStepProps {
  operationData: any[];
  priceData: any[];
}

const OperationPriceStep: React.FC<OperationPriceStepProps> = ({
  operationData,
  priceData,
}) => {
  // 开停机表格列配置
  const operationColumns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 150,
      align: 'center',
    },
    {
      title: '开停机状态',
      dataIndex: 'flag',
      key: 'flag',
      width: 150,
      align: 'center',
    },
  ];

  // 电价表格列配置
  const priceColumns: ColumnsType<any> = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 150,
      align: 'center',
    },
    {
      title: '实时电价(元/MWh)',
      dataIndex: 'price',
      key: 'price',
      width: 120,
      align: 'center',
    },
    {
      title: '中长期电价(元/MWh)',
      dataIndex: 'longPrice',
      key: 'longPrice',
      width: 120,
      align: 'center',
    },
  ];

  return (
    <div className={styles.stepContent}>
      <h3 className={styles.stepTitle}>开停机与电价信息</h3>

      {/* 开停机信息表格 */}
      <div style={{ marginBottom: '20px' }}>
        <h4 style={{ margin: '10px 0', fontSize: '14px' }}>开停机信息</h4>
        <CustomTable
          dataSource={operationData}
          columns={operationColumns}
          className={styles.table}
          size="small"
          pagination={false}
          scroll={{ y: 200 }}
        />
      </div>

      {/* 电价信息表格 */}
      <div>
        <h4 style={{ margin: '10px 0', fontSize: '14px' }}>电价信息</h4>
        <div
          style={{
            width: '100%',
            maxHeight: '200px',
            overflowY: 'scroll',
          }}
        >
          <CustomTable
            dataSource={priceData}
            columns={priceColumns}
            className={styles.table}
            size="small"
            pagination={false}
          />
        </div>
      </div>
    </div>
  );
};

export default OperationPriceStep;
