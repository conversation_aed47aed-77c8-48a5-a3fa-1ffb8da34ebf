import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import { ITradeData, ITradeStrategy } from '@/pages/MarketTrading/typing';
import { AGetTradeStrategy } from '@/services/marketTrading';
import px from '@/utils/px.js';
import { useRequest } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import ResultInfo from '../ResultInfo';
import StrategyInfo from '../StrategyInfo';

import styles from './index.sass';
import { resultDataFormat, strategyDataFormat } from './option';
interface Props {
  tradeData?: ITradeData;
  open: boolean;
  onCancel: () => void;
}

const Index: React.FC<Props> = (props: Props) => {
  const { open, onCancel, tradeData } = props;
  const { data, run } = useRequest(AGetTradeStrategy, {
    manual: true,
  });
  const [currentStrategy, setCurrentStrategy] = useState<ITradeStrategy>();
  const [currentResult, setCurrentResult] = useState<ITradeStrategy>();
  const [openStrategy, setOpenStrategy] = useState(false);
  const [openResult, setOpenResult] = useState(false);

  useEffect(() => {
    // 获取策略数据  填入
    if (open === true && tradeData?.sellId) {
      run({ sellId: tradeData.sellId });
    }
  }, [open]);

  const onCancelStrategy = () => {
    setOpenStrategy(false);
  };
  const onCancelResult = () => {
    setOpenResult(false);
  };
  const clickEditStrategy = () => {
    setOpenStrategy(true);
    setCurrentStrategy(data);
  };
  const clickEditResult = () => {
    setOpenResult(true);
    setCurrentResult(data);
  };
  const onEditResult = () => {
    setOpenResult(false);
    run({ sellId: tradeData.sellId });
  };
  const onEditStrategy = () => {
    run({ sellId: tradeData.sellId });
    setOpenStrategy(false);
  };
  return (
    <>
      <CustomModal
        open={open}
        title="交易策略详情"
        onCancel={onCancel}
        footer={null}
        className={styles.box}
        width={px(1280)}
        centered={true}
      >
        <div className={styles.strategy}>
          <div className={styles.strategyHead}>
            <div className={styles.title}>交易策略</div>
            <CustomButton onClick={clickEditStrategy} className={styles.button}>
              编辑策略
            </CustomButton>
            <CustomButton>报量报价</CustomButton>
          </div>
          <div className={styles.content}>
            {strategyDataFormat.map(({ name, w, hw, attr }) => {
              return (
                <div className={styles.strategyData} key={name}>
                  <div className={styles.label} style={{ width: px(w) + 'px' }}>
                    {name}：
                  </div>
                  {data?.applyBuyRecord && (
                    <div
                      className={styles.value}
                      style={{ width: px(hw) + 'px' }}
                    >
                      {attr === 'time'
                        ? data?.applyBuyRecord.startTime +
                          '--' +
                          data?.applyBuyRecord.endTime
                        : data?.applyBuyRecord[attr]}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
        <div className={styles.result}>
          <div className={styles.resultHead}>
            <div className={styles.title}>竞价结果</div>
            <CustomButton className={styles.button} onClick={clickEditResult}>
              编辑结果
            </CustomButton>
            <CustomButton className={styles.button}>查看调度计划</CustomButton>
          </div>
          <div className={styles.content}>
            {resultDataFormat.map(({ name, w, hw, attr }) => {
              return (
                <div className={styles.strategyData} key={name}>
                  <div className={styles.label} style={{ width: px(w) + 'px' }}>
                    {name}：
                  </div>
                  {data?.winbidRecord && (
                    <div
                      className={styles.value}
                      style={{ width: px(hw) + 'px' }}
                    >
                      {attr === 'time'
                        ? data?.winbidRecord.startTime +
                          '--' +
                          data?.winbidRecord.endTime
                        : data?.winbidRecord[attr]}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </CustomModal>
      <StrategyInfo
        open={openStrategy}
        tradeStrategy={currentStrategy}
        onCancel={onCancelStrategy}
        onSuccess={onEditStrategy}
      ></StrategyInfo>
      <ResultInfo
        open={openResult}
        resultInfo={currentResult}
        onCancel={onCancelResult}
        onSuccess={onEditResult}
      ></ResultInfo>
    </>
  );
};

export default Index;
